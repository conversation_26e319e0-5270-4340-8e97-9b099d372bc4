import { clearTokenRefresh, scheduleTokenRefresh } from "@/lib/authManager";
import { create } from "zustand";
import { persist } from "zustand/middleware";
import { jwtDecode } from "jwt-decode";
import { User } from "@/types/auth";
import { auth } from "@/lib/auth";

interface AuthState {
  user: User | null;
  role: string | null;
  setAuth: (
    user: User,
    token: string,
    expiresIn: number,
    refreshToken: string,
  ) => void;
  logout: () => void;
  isAdmin: () => boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      role: null,

      setAuth: (user, token, expiresIn, refreshToken) => {
        localStorage.setItem("token", token);
        localStorage.setItem("refreshToken", refreshToken);

        scheduleTokenRefresh(expiresIn);

        const decoded: any = jwtDecode(token);
        
        set({
          user,
          role: decoded.roles?.[0] || null,
        });
      },

      logout: () => {
        const { clear } = auth;
        clear();
        clearTokenRefresh();
        set({ user: null, role: null });
      },
      isAdmin: () => {
        const token = localStorage.getItem("token");

        if (!token) return false;

        const decoded: any = jwtDecode(token);

        if (!decoded.roles || !Array.isArray(decoded.roles)) return false;

        const result = decoded.roles
          .map((r: string) => r.toLowerCase())
          .some((r: string) => r.includes("admin"));

        return result;
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({ user: state.user, role: state.role }),
    },
  ),
);
