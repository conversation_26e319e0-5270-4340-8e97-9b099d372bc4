"use client";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import useGetTestimonials, {
  Testimonial,
} from "@/hooks/testimonials/useGetTestimonials";
import { EyeIcon, Quote, Star } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";

export default function TestimonialList() {
  const [pageSize, setPageSize] = useState(10);
  const { data, isLoading, isError } = useGetTestimonials({
    pageSize,
  });
  const locale = useLocale();
  const t = useTranslations();

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        <TestimonialSkeleton />
        <TestimonialSkeleton />
        <TestimonialSkeleton />
      </div>
    );
  }

  if (isError) {
    return (
      <Alert className="mx-auto max-w-lg border-red-500 bg-red-400/30 text-red-500">
        <AlertTitle>{locale === "ar" ? "خطأ" : "Error"}</AlertTitle>
        <AlertDescription className="text-red-500">
          {locale === "ar"
            ? "حدث خطأ. يرجى المحاولة مرة أخرى لاحقا."
            : "Something went wrong. Please try again later."}
        </AlertDescription>
      </Alert>
    );
  }

  if (data?.items && data?.items?.length <= 3) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {data?.items.map((testimonial) => (
          <TestimonialCard key={testimonial.id} testimonial={testimonial} />
        ))}
      </div>
    );
  }

  return (
    <div className="md:px-12">
      <Carousel
        opts={{
          align: "center",
          direction: locale === "ar" ? "rtl" : "ltr",
        }}
      >
        <CarouselContent className="-ml-2 py-3 md:-ml-4">
          {data?.items.map((testimonial) => (
            <CarouselItem
              key={testimonial.id}
              className="basis-[calc(100%)] pl-2 md:basis-[calc(100%/2-20px)] md:pl-4 lg:basis-[calc(100%/3-20px)]"
            >
              <TestimonialCard testimonial={testimonial} />
            </CarouselItem>
          ))}
          {data?.hasNextPage && (
            <CarouselItem className="basis-[calc(100%-20px)] pl-2 md:basis-[calc(100%/2-20px)] md:pl-4 lg:basis-[calc(100%/3-20px)]">
              <Button
                onClick={() => setPageSize((prev) => prev + 10)}
                variant="outline"
                className="h-full w-full text-lg"
              >
                {t("global.showMore")}
                <EyeIcon />
              </Button>
            </CarouselItem>
          )}
        </CarouselContent>

        <CarouselPrevious className="absolute top-1/2 right-[auto] left-[auto] -translate-y-1/2 ltr:left-2 ltr:md:-left-12 rtl:right-2 rtl:md:-right-12 rtl:[&_svg]:rotate-180" />

        <CarouselNext className="absolute top-1/2 right-[auto] left-[auto] -translate-y-1/2 ltr:right-2 ltr:md:-right-12 rtl:left-2 rtl:md:-left-12 rtl:[&_svg]:rotate-180" />
      </Carousel>
    </div>
  );
}

const TestimonialCard = ({ testimonial }: { testimonial: Testimonial }) => {
  return (
    <Card className="group dark:bg-background/80 relative border-0 bg-[#FEFDFD] py-10 transition-all duration-500 *:select-none">
      <CardContent className="relative overflow-hidden p-8">
        <div className="absolute top-4 right-4 opacity-10">
          <Quote className="text-primary h-8 w-8" />
        </div>

        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-1">
            {[...Array(testimonial.rating)].map((_, i) => (
              <Star
                key={i}
                className="h-5 w-5 fill-yellow-400 text-yellow-400"
              />
            ))}
          </div>
        </div>

        <blockquote className="text-foreground relative mb-6 leading-relaxed">
          <span className="text-primary absolute -top-2 -left-1 font-serif text-4xl opacity-20">
            "
          </span>
          <span className="relative z-10">{testimonial.comment}</span>
        </blockquote>

        <div className={`flex items-center gap-x-4`}>
          <Avatar className="ring-primary h-14 w-14 ring-1 ring-offset-1">
            <AvatarFallback className="bg-brand-teal-light text-white shadow">
              {testimonial.firstName[0]?.toLocaleUpperCase() +
                " " +
                testimonial.lastName[0]?.toLocaleUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="text-foreground group-hover:text-primary font-bold transition-colors duration-300">
              {testimonial.firstName + " " + testimonial.lastName}
            </p>
          </div>
        </div>
      </CardContent>
      <div className="via-primary/30 absolute bottom-0 left-0 h-1 w-full scale-x-0 transform bg-gradient-to-r from-transparent to-transparent transition-transform duration-300 group-hover:scale-x-100" />
    </Card>
  );
};

const TestimonialSkeleton = () => {
  return (
    <div className="border-border/50 bg-card shadow-soft relative overflow-hidden rounded-lg border p-10">
      <div className="bg-muted/30 absolute top-4 right-4 h-8 w-8 rounded-full" />

      <div className="mb-6 flex space-x-1">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="bg-muted/30 h-5 w-5 rounded-full" />
        ))}
      </div>

      <div className="mb-6 space-y-2">
        <div className="bg-muted/30 h-4 w-full rounded" />
        <div className="bg-muted/30 h-4 w-5/6 rounded" />
        <div className="bg-muted/30 h-4 w-4/6 rounded" />
      </div>

      <div className="flex items-center gap-x-4">
        <div className="bg-muted/30 h-14 w-14 rounded-full" />
        <div className="bg-muted/30 h-4 w-24 rounded" />
      </div>

      <div className="bg-muted/20 absolute bottom-0 left-0 h-1 w-full" />
    </div>
  );
};
