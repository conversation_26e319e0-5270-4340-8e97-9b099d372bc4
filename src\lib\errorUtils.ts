import { isAxiosError } from "axios";

export const errorThrower = (err: any) => {
  console.error(err);

  if (isAxiosError(err)) {
    if (err.response?.status === 400) {
      throw new Error("طلب غير صالح (400)");
    }

    if (err.response?.status === 401 || err.response?.status === 403) {
      throw new Error("غير مصرح به (المتصل غير مُصادق عليه كمسؤول)");
    }

    if (err.response?.status === 409) {
      throw new Error("الطلب موجود بالفعل (409)");
    }

    if (err.response?.status === 404) {
      throw new Error("غير موجود (404)");
    }

    if (err.response?.status === 500) {
      throw new Error("حدث خطأ غير متوقع من الخادم");
    }
  }

  throw new Error(err instanceof Error ? err.message : "حدث خطأ غير متوقع");
};
