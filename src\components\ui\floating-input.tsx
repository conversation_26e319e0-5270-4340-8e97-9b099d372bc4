"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";
import { useLocale } from "next-intl";

export interface FloatingInputProps extends React.ComponentProps<"input"> {
  label: string;
  icon?: LucideIcon;
  error?: string;
  type?: string;
}

export interface FloatingTextareaProps extends React.ComponentProps<"textarea"> {
  label: string;
  icon?: LucideIcon;
  error?: string;
  type: "textarea";
}

type FloatingInputAllProps = FloatingInputProps | FloatingTextareaProps;

const FloatingInput = React.forwardRef<HTMLInputElement | HTMLTextAreaElement, FloatingInputAllProps>(
  ({ className, type = "text", label, icon: Icon, error, ...props }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const [hasValue, setHasValue] = React.useState(false);
    const locale = useLocale();

    const handleFocus = () => setIsFocused(true);
    const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setIsFocused(false);
      setHasValue(e.target.value.length > 0);
      props.onBlur?.(e as any);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setHasValue(e.target.value.length > 0);
      props.onChange?.(e as any);
    };

    const isLabelFloating = isFocused || hasValue;
    const isRTL = locale === "ar";

    return (
      <div className="relative">
        <div className="relative">
          {/* Icon */}
          {Icon && (
            <Icon
              className={cn(
                "absolute h-4 w-4 transition-all duration-200 ease-in-out",
                isRTL ? "right-3" : "left-3",
                isLabelFloating
                  ? "text-primary top-3"
                  : "text-muted-foreground top-1/2 -translate-y-1/2",
              )}
            />
          )}

          {/* Input or Textarea */}
          {type === "textarea" ? (
            <textarea
              ref={ref as React.Ref<HTMLTextAreaElement>}
              className={cn(
                "peer border-input w-full rounded-md border bg-transparent px-3 py-3 text-base placeholder-transparent transition-all duration-200 ease-in-out outline-none resize-none",
                "focus:border-primary focus:ring-primary/20 focus:ring-2",
                Icon && (isRTL ? "pr-10" : "pl-10"),
                error &&
                  "border-destructive focus:border-destructive focus:ring-destructive/20",
                className,
              )}
              placeholder={label}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onChange={handleChange}
              {...(props as React.ComponentProps<"textarea">)}
            />
          ) : (
            <input
              type={type}
              ref={ref as React.Ref<HTMLInputElement>}
              className={cn(
                "peer border-input w-full rounded-md border bg-transparent px-3 py-3 text-base placeholder-transparent transition-all duration-200 ease-in-out outline-none",
                "focus:border-primary focus:ring-primary/20 focus:ring-2",
                Icon && (isRTL ? "pr-10" : "pl-10"),
                error &&
                  "border-destructive focus:border-destructive focus:ring-destructive/20",
                className,
              )}
              placeholder={label}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onChange={handleChange}
              {...(props as React.ComponentProps<"input">)}
            />
          )}

          {/* Floating Label */}
          <label
            className={cn(
              "pointer-events-none absolute transition-all duration-200 ease-in-out",
              isRTL ? "right-3" : "left-3",
              Icon && (isRTL ? "right-10" : "left-10"),
              isLabelFloating
                ? "bg-background text-primary -top-2 px-1 text-xs"
                : "text-muted-foreground top-1/2 -translate-y-1/2 text-base",
            )}
          >
            {label}
          </label>
        </div>

        {/* Error Message */}
        {error && <p className="text-destructive mt-1 text-xs">{error}</p>}
      </div>
    );
  },
);

FloatingInput.displayName = "FloatingInput";

export { FloatingInput };
