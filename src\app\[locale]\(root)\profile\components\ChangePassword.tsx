"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { FloatingPasswordInput } from "@/components/ui/floating-password-input";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { useChangePassword } from "@/hooks/account";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Lock, Shield, Loader2 } from "lucide-react";
import { useTranslations, useLocale } from "next-intl";
import React from "react";

// Validation schema for change password - will be created inside component to access translations
const createChangePasswordSchema = (t: any) => z.object({
  currentPassword: z.string().min(1, t("currentPasswordRequired")),
  newPassword: z
    .string()
    .min(8, t("passwordMinLength"))
    .regex(/[A-Z]/, t("passwordUppercase"))
    .regex(/[a-z]/, t("passwordLowercase"))
    .regex(/[0-9]/, t("passwordNumber"))
    .regex(/[^A-Za-z0-9]/, t("passwordSpecial")),
  confirmPassword: z.string().min(1, t("confirmPasswordRequired")),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: t("passwordsDontMatch"),
  path: ["confirmPassword"],
});

type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;

export default function ChangePassword() {
  const t = useTranslations("account.changePassword");
  const locale = useLocale();
  const isRTL = locale === "ar";
  const changePasswordMutation = useChangePassword();

  const changePasswordSchema = createChangePasswordSchema(t);

  const form = useForm<ChangePasswordFormData>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (data: ChangePasswordFormData) => {
    try {
      await changePasswordMutation.mutateAsync({
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
      });
      // Reset form on success
      form.reset();
    } catch (error) {
      // Error handling is done in the hook
      console.error("Password change failed:", error);
    }
  };

  return (
    <Card className="bg-background">
      <CardHeader>
        <h2 className="text-xl font-semibold">{t("title")}</h2>
        <p className="text-sm text-muted-foreground">
          {t("description")}
        </p>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Root form errors */}
            {form.formState.errors.root?.message && (
              <div className="bg-destructive/10 text-destructive rounded-md p-3 text-sm">
                {form.formState.errors.root.message}
              </div>
            )}

            <FormField
              control={form.control}
              name="currentPassword"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormControl>
                    <FloatingPasswordInput
                      label={t("currentPassword")}
                      icon={Lock}
                      error={fieldState.error?.message}
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="newPassword"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormControl>
                    <FloatingPasswordInput
                      label={t("newPassword")}
                      icon={Lock}
                      error={fieldState.error?.message}
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormControl>
                    <FloatingPasswordInput
                      label={t("confirmPassword")}
                      icon={Lock}
                      error={fieldState.error?.message}
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="bg-primary text-primary-foreground hover:bg-primary/90 h-12 w-full"
              disabled={changePasswordMutation.isPending}
            >
              {changePasswordMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t("updating")}
                </>
              ) : (
                <>
                  <Shield className="mr-2 h-4 w-4" />
                  {t("updatePassword")}
                </>
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
