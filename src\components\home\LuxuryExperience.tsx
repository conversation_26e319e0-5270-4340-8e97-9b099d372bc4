"use client";

import { Check, Play, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { Button } from "../ui/button";

const LuxuryExperience = () => {
  const t = useTranslations();
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  const features = [
    t("luxuryExperience.features.concierge"),
    t("luxuryExperience.features.amenities"),
    t("luxuryExperience.features.dining"),
    t("luxuryExperience.features.spa"),
  ];

  return (
    <section className="px-4 py-20 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          <div className="space-y-8">
            <div>
              <h2 className="mb-4 text-4xl font-bold md:text-5xl">
                {t("luxuryExperience.title")}
              </h2>
              <h3
                className="mb-6 text-2xl font-semibold md:text-3xl"
                style={{ color: "#279fc7" }}
              >
                {t("luxuryExperience.subtitle")}
              </h3>
              <p className="text-muted-foreground text-lg leading-relaxed">
                {t("luxuryExperience.description")}
              </p>
            </div>

            <div className="space-y-4">
              {features.map((feature, index) => (
                <div key={index} className={`flex items-center gap-3`}>
                  <div
                    className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full shadow-sm"
                    style={{ backgroundColor: "#279fc7" }}
                  >
                    <Check className="h-4 w-4 text-white" strokeWidth={2} />
                  </div>
                  <span className="font-medium">{feature}</span>
                </div>
              ))}
            </div>

            <Button
              size="lg"
              onClick={() => setIsVideoModalOpen(true)}
              className={
                "group h-12 transform items-center rounded-full font-medium text-white shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
              }
            >
              <span className="text-sm">
                {t("luxuryExperience.watchVideo")}
              </span>
              <div className="bg-primary/10 flex h-6 w-6 items-center justify-center rounded-full">
                <Play className="h-3 w-3 transition-transform duration-300 group-hover:scale-110" />
              </div>
            </Button>
          </div>

          <div className="relative">
            <div className="relative overflow-hidden rounded-2xl shadow-2xl">
              <img
                alt={t("luxuryExperience.imageAlt")}
                className="h-96 w-full object-cover lg:h-[500px]"
                src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
              />
              <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                <button
                  onClick={() => setIsVideoModalOpen(true)}
                  className="group flex h-20 w-20 items-center justify-center rounded-full border border-white/20 bg-white/95 shadow-xl backdrop-blur-sm transition-all duration-300 hover:scale-110 hover:bg-white"
                >
                  <Play className="ml-1 h-8 w-8 text-slate-700 transition-transform duration-300 group-hover:scale-110" />
                </button>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-blue-400/20 blur-xl"></div>
            <div className="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-slate-400/20 blur-xl"></div>
          </div>
        </div>
      </div>

      {/* Video Modal */}
      {isVideoModalOpen && (
        <div
          className="animate-in fade-in fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm duration-300"
          onClick={() => setIsVideoModalOpen(false)}
        >
          <div
            className="animate-in zoom-in-95 slide-in-from-bottom-4 relative mx-4 w-full max-w-4xl duration-300"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <button
              onClick={() => setIsVideoModalOpen(false)}
              className="animate-in slide-in-from-top-2 absolute -top-12 right-0 flex h-10 w-10 items-center justify-center rounded-full bg-white/20 text-white transition-all delay-150 duration-300 hover:scale-110 hover:rotate-90 hover:bg-white/30"
            >
              <X className="h-6 w-6" />
            </button>

            {/* Video container */}
            <div className="hover:shadow-3xl relative aspect-video w-full transform overflow-hidden rounded-2xl bg-slate-800 shadow-2xl transition-all duration-300">
              <video
                className="h-full w-full object-cover"
                controls
                autoPlay
                muted
                playsInline
              >
                <source src={"/Luxury.mp4"} type="video/mp4" />
                <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
                  <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-white/20">
                    <Play className="ml-1 h-8 w-8 text-white" />
                  </div>
                  <h3 className="mb-2 text-xl font-semibold">
                    Your browser does not support the video tag
                  </h3>
                  <p className="max-w-md text-center text-white/70">
                    Please update your browser to view this video
                  </p>
                </div>
              </video>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default LuxuryExperience;
