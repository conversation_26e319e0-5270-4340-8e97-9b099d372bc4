import { Button } from "@/components/ui/button";
import { useLogin } from "@/hooks/auth";
import { createAuthValidations } from "@/lib/validations/auth";
import { useAuthDialogStore } from "@/store/authDialogStore";
import { zodResolver } from "@hookform/resolvers/zod";
import { isAxiosError } from "axios";
import { useLocale, useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import EmailInput from "../ui/email-input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import PasswordInput from "../ui/password-input";
import SigninWithGoogleButton from "./SigninWithGoogleButton";
import { useState } from "react";

export default function SigninForm() {
  const searchParams = useSearchParams();
  const redirectedEmail = searchParams.get("confirmedEmail");

  const t = useTranslations();
  const locale = useLocale();
  const isRTL = locale === "ar";

  const { signinSchema } = createAuthValidations(t);
  const { mutate, isPending } = useLogin();
  const { closeDialog, setMessage, setAuthType } = useAuthDialogStore();

  const router = useRouter();

  const form = useForm<z.infer<typeof signinSchema>>({
    resolver: zodResolver(signinSchema),
    defaultValues: {
      email: redirectedEmail || "",
      password: "",
    },
  });

  const onSubmit = (values: z.infer<typeof signinSchema>) => {
    mutate(values, {
      onSettled: () => {
        router.replace(`/${locale}`);
      },
      onSuccess: () => {
        form.reset();
        closeDialog();
        setMessage(null);
      },
      onError: (error) => {
        console.error(error);

        if (error && isAxiosError(error)) {
          if (
            error.response?.data.status === 400 ||
            error.response?.data.status === 401
          ) {
            form.setError("root", {
              message: t("login.error"),
            });
          } else {
            form.setError("root", {
              message: t("errors.generalError"),
            });
          }
        } else {
          form.setError("root", {
            message: t("errors.generalError"),
          });
        }
      },
    });
  };

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          noValidate
          className="space-y-4"
        >
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel>{t("auth.email")}</FormLabel>
                <FormControl>
                  <EmailInput placeholder={t("auth.enterEmail")} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel>{t("auth.password")}</FormLabel>
                <FormControl>
                  <PasswordInput {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {form.formState.errors.root?.message && (
            <Alert
              variant="destructive"
              className="border-red-500 bg-red-200/50"
            >
              <AlertTitle>{isRTL ? "خطأ" : "Error"}</AlertTitle>
              <AlertDescription>
                {form.formState.errors.root?.message}
              </AlertDescription>
            </Alert>
          )}

          <Button
            onClick={() => {
              setAuthType("reset-password");
            }}
            className="!px-0 !py-1"
            variant="link"
            type="button"
          >
            {t("auth.forgotPassword")}
          </Button>

          <Button
            type="submit"
            className="w-full bg-[#279fc7] text-white hover:bg-[#279fc7]/90"
            disabled={isPending}
          >
            {isPending ? t("auth.signingIn") : t("auth.signIn")}
          </Button>

          {/* Social Login */}
          <div className="space-y-3">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2">
                  {isRTL ? "أو" : "Or"}
                </span>
              </div>
            </div>

            <SigninWithGoogleButton />
          </div>
        </form>
      </Form>
    </>
  );
}
