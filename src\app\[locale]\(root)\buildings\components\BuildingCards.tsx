import { Building } from "@/types/buildings";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { Building2, Users } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { Card } from "@/components/ui/card";

export default function BuildingCards({ data }: { data: Building[] }) {
  const locale = useLocale();
  const t = useTranslations("buildings");

  return (
    <div className="grid w-full grid-cols-[repeat(auto-fit,minmax(320px,1fr))] gap-6">
      {data.map((build) => (
        <Card
          key={build.id}
          className="group overflow-hidden transition-all duration-300 hover:shadow-lg"
        >
          <div className="relative">
            <Image
              alt={locale === "ar" ? build.nameAr : build.nameEn}
              className="h-64 w-full object-cover transition-transform duration-500"
              src={build.image}
              width={400}
              height={256}
            />
            <div className="absolute bottom-4 left-4 flex items-center gap-2 rounded-lg bg-white/90 px-3 py-2 backdrop-blur-sm">
              <Building2 className="text-primary h-5 w-5" />
              <span className="font-semibold text-gray-800">
                {locale === "ar" ? build.nameAr : build.nameEn}
              </span>
            </div>
          </div>

          <div className="space-y-3 p-4">
            <p className="line-clamp-3 text-sm text-gray-600">
              {locale === "ar" ? build.descriptionAr : build.descriptionEn}
            </p>

            <div className="text-primary flex items-center gap-2 text-sm font-medium">
              <Users className="h-4 w-4" />
              <span>
                {build.unitsCount} {t("units", { defaultValue: "Units" })}
              </span>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}
