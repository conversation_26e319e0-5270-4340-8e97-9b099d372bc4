import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, MessageCircle } from "lucide-react";
import { getTranslations } from "next-intl/server";
import Link from "next/link";

const ContactSupportSection: React.FC = async () => {
  const t = await getTranslations();

  return (
    <div className="mt-12 text-center">
      <div className="from-primary/5 via-primary/10 to-primary/5 rounded-2xl bg-gradient-to-r p-8">
        <MessageCircle className="text-primary mx-auto mb-4 h-12 w-12" />
        <h3 className="text-foreground mb-2 text-xl font-bold">
          {t("faq.stillHaveQuestions")}
        </h3>
        <p className="text-muted-foreground mb-6">
          {t("faq.supportDescription")}
        </p>
        <div className="flex justify-center">
          <Button
            asChild
            size="lg"
            variant="outline"
            className="border-primary text-primary hover:bg-primary transition-all duration-300 hover:text-white"
          >
            <Link href="/contact">
              {t("faq.contactSupport")}
              <ArrowRight className="h-4 w-4 rtl:rotate-180" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ContactSupportSection;
