import api from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";

export default function useGetTestimonials({ pageSize }: { pageSize: number }) {
  return useQuery({
    queryKey: ["total-reviews", pageSize],
    queryFn: () => getTestimonials({ pageSize }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    retryDelay: 1000,
  });
}

interface GetTestimonialsParams {
  pageNumber?: number;
  pageSize?: number;
}

export type Testimonial = {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  rating: number;
  comment: string;
  createdAt: string;
  status: number;
  isOwner: boolean;
};

type GetTestimonialsResponse = { items: Testimonial[] };

const getTestimonials = async ({
  pageNumber = 1,
  pageSize = 10,
}: GetTestimonialsParams = {}) => {
  try {
    const response = await api.get<GetTestimonialsResponse>(
      "/TotalReviews/user",
      {
        params: {
          PageNumber: pageNumber,
          PageSize: pageSize,
        },
      },
    );

    return response.data;
  } catch (err) {
    console.error(err);
    throw err;
  }
};
