"use client";

import LoginFirstDialog from "@/components/auth/LoginFirstDialog";
import FormErrors from "@/components/FormErrors";
import FormFeedback from "@/components/FormFeedback";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import useCreateReview from "@/hooks/testimonials/useCreateReview";
import { useAuthStore } from "@/store/authState";
import { zodResolver } from "@hookform/resolvers/zod";
import { Send, Star } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const CreateReviewButton = ({ className }: { className?: string }) => {
  const t = useTranslations();

  const [open, setOpen] = useState(false);
  const [isLoginFirstDialogOpen, setIsLoginFirstDialogOpen] = useState(false);

  const user = useAuthStore((state) => state.user);

  const createReviewSchema = z.object({
    rating: z
      .number()
      .min(1, t("testimonials.addReview.error.ratingRequired"))
      .max(5, t("testimonials.addReview.error.ratingRequired")),
    comment: z
      .string()
      .trim()
      .min(10, t("testimonials.addReview.error.reviewTooShort")),
  });

  const form = useForm<z.infer<typeof createReviewSchema>>({
    resolver: zodResolver(createReviewSchema),
    defaultValues: {
      rating: 0,
      comment: "",
    },
  });

  const {
    mutate,
    error,
    isError,
    isSuccess,
    isPending,
    reset: resetMutation,
  } = useCreateReview();

  const onSubmit = (data: z.infer<typeof createReviewSchema>) => {
    mutate(data, {
      onSuccess: () => setOpen(false),
    });
  };

  return (
    <>
      <Dialog
        open={open}
        onOpenChange={(open) => {
          if (!open) form.reset();
          setOpen(open);
        }}
      >
        <Button
          onClick={() => {
            if (user) {
              setOpen(true);
            } else {
              setIsLoginFirstDialogOpen(true);
            }
          }}
          className={className}
        >
          {t("testimonials.addReview.button")}
        </Button>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("testimonials.addReview.title")}</DialogTitle>
            <DialogDescription>
              {t("testimonials.addReview.description")}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="mt-4 space-y-6"
            >
              <FormField
                control={form.control}
                name="rating"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("testimonials.addReview.rating")}</FormLabel>
                    <FormControl>
                      <div className={`flex items-center justify-center gap-2`}>
                        {[1, 2, 3, 4, 5].map((star) => (
                          <button
                            key={star}
                            type="button"
                            className="text-2xl transition-transform duration-200 hover:scale-110"
                            onClick={() => field.onChange(star)}
                          >
                            <Star
                              className={`h-8 w-8 transition-colors duration-200 ${
                                star <= field.value
                                  ? "fill-yellow-400 text-yellow-400"
                                  : "fill-gray-300 text-gray-300 hover:fill-yellow-200 hover:text-yellow-200"
                              }`}
                            />
                          </button>
                        ))}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="comment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("testimonials.addReview.review")}</FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {isError && <FormErrors error={error} />}
              <DialogFooter>
                <DialogClose asChild>
                  <Button disabled={isPending} type="button" variant="outline">
                    {t("global.cancel")}
                  </Button>
                </DialogClose>
                <Button
                  disabled={isPending}
                  type="submit"
                  className="text-white"
                >
                  {isPending
                    ? t("testimonials.addReview.submitting")
                    : t("testimonials.addReview.submit")}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <LoginFirstDialog
        open={isLoginFirstDialogOpen}
        onOpenChangeAction={(open) => setIsLoginFirstDialogOpen(open)}
      />

      <FormFeedback
        open={isSuccess}
        onOpenChange={(open) => {
          if (!open) {
            form.reset();
            resetMutation();
          }
        }}
        title={t("testimonials.addReview.success.title")}
        description={t("testimonials.addReview.success.description")}
        icon={Send}
      />
    </>
  );
};

export default CreateReviewButton;
