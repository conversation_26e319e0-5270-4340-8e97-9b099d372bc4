import logoImage from "@/../public/logo.png";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useAuthDialogStore } from "@/store/authDialogStore";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "../ui/dialog";
import SigninForm from "./SigninForm";
import SignupForm from "./SignupForm";
import ResetPasswordForm from "./ResetPasswordForm";

const AuthDialog = () => {
  const t = useTranslations();

  const { isOpen, authType, setAuthType, openDialog, closeDialog, message } =
    useAuthDialogStore();

  const redirectedEmail = useSearchParams().get("confirmedEmail");

  const locale = useLocale();
  const router = useRouter();

  useEffect(() => {
    if (redirectedEmail) {
      openDialog("signin");
    }
  }, [redirectedEmail]);

  const activeTab = authType;

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          closeDialog();
          router.replace(`/${locale}`, { scroll: false });
        }
      }}
    >
      <DialogContent className="!max-w-md border-0 p-0 shadow-2xl">
        <div className="flex justify-center pt-8 pb-5">
          <Image
            src={logoImage}
            alt="Hala Logo"
            className="h-12 w-12 object-contain"
          />
        </div>

        <DialogTitle asChild>
          <h2 className="mb-4 text-center text-xl font-bold">
            {authType === "signin"
              ? t("auth.signIn")
              : authType === "signup"
                ? t("auth.signUp")
                : ""}
          </h2>
        </DialogTitle>

        <DialogDescription className="sr-only">
          {authType === "signin" ? t("auth.signIn") : t("auth.signUp")}
        </DialogDescription>

        {/* Content */}
        <div className="px-6 pb-6">
          {message && (
            <div
              className={cn("my-4 rounded-2xl border p-4", {
                "border-green-200 bg-green-100 text-green-600":
                  message.type === "success",
                "border-red-300 bg-red-100 text-red-600":
                  message.type === "error",
              })}
            >
              <p className="text-lg">{message.text}</p>
            </div>
          )}

          {/* Tab Buttons */}
          <div className="bg-card mb-6 flex gap-1 rounded-lg p-1">
            <Button
              variant={authType === "signin" ? "default" : "ghost"}
              onClick={() => setAuthType("signin")}
              className={cn("flex-1", {
                "hover:bg-secondary": authType !== "signin",
              })}
            >
              {t("auth.signIn")}
            </Button>
            <Button
              variant={authType === "signup" ? "default" : "ghost"}
              onClick={() => setAuthType("signup")}
              className={cn("flex-1", {
                "hover:bg-secondary": authType !== "signup",
              })}
            >
              {t("auth.signUp")}
            </Button>
          </div>

          {/* Login Form */}
          {activeTab === "signin" && <SigninForm />}

          {/* Register Form */}
          {activeTab === "signup" && <SignupForm />}

          {/* Reset Password Form */}
          {activeTab === "reset-password" && <ResetPasswordForm />}

          {/* Separator */}
          <div className="my-6">
            <div className="mt-4 text-center">
              <span className="text-sm">{t("auth.byContinuing")} </span>
              <Button
                variant="link"
                className="h-auto p-0 text-sm text-[#279fc7] hover:text-[#279fc7]/90"
              >
                {t("auth.termsOfService")}
              </Button>{" "}
              <span className="text-sm">{t("auth.and")} </span>
              <Button
                variant="link"
                className="h-auto p-0 text-sm text-[#279fc7] hover:text-[#279fc7]/90"
              >
                {t("auth.privacyPolicy")}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AuthDialog;
