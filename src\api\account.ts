import api from "@/lib/axios";

interface ProfileResponse {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  status: number;
  birthDateGregorian: string;
  birthDateHijri: string;
  nationality: string;
  filesUrl: string[];
  profileImageUrl: string;
}

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

interface UpdateProfileRequest {
  firstName: string;
  lastName: string;
}

export interface CompleteProfileRequest {
  birthDateGregorian: string;
  birthDateHijri: string;
  nationality: string;
  files?: File[];
}

export const accountApi = {
  changePassword: (data: ChangePasswordRequest) => {
    return api.post("/UserAccount/change-password", data);
  },

  getProfile: async () => {
    const { data } = await api.get<ProfileResponse>("/UserAccount/Profile");
    return data;
  },

  updateProfile: (data: UpdateProfileRequest) => {
    return api.put("/UserAccount/profile", data);
  },

  updateProfileImage: (imageFile: File) => {
    const formData = new FormData();
    formData.append("File", imageFile);
    return api.put("/UserAccount/change-ProfileImage", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  completeProfile: (
    birthDateGregorian: string,
    birthDateHijri: string,
    nationality: string,
    files?: File[],
    onUploadProgress?: (progressEvent: ProgressEvent) => void,
  ) => {
    const formData = new FormData();
    formData.append("BirthDateGregorian", birthDateGregorian);
    formData.append("BirthDateHijri", birthDateHijri);
    formData.append("Nationality", nationality);

    if (files) {
      files.forEach((file) => {
        formData.append(`Files`, file);
      });
    }
    return api.patch<void>(`/UserAccount/complete`, formData, {
      onUploadProgress: onUploadProgress as
        | ((progressEvent: import("axios").AxiosProgressEvent) => void)
        | undefined,
    });
  },
};
