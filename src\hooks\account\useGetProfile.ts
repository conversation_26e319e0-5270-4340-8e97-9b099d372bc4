import { useQuery } from "@tanstack/react-query";
import { accountApi } from "@/api/account";
import { useMemo } from "react";

export const useProfile = () => {
  const query = useQuery({
    queryKey: ["profile"],
    queryFn: accountApi.getProfile,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  const profileImg = useMemo(() => {
    if (query.data?.profileImageUrl) {
      return query?.data.profileImageUrl;
    }
    return "https://github.com/evilrabbit.png";
  }, [query.data?.profileImageUrl]);

  const profileStatus = useMemo(() => {
    return query?.data?.status;
  }, [query.data?.status]);

  return {
    ...query,
    profileImg,
    profileStatus,
    profile: query.data,
  };
};
