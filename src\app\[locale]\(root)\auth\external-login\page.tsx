"use client";

import GenericPageLoading from "@/components/GenericPageLoading";
import { useAuthStore } from "@/store/authState";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function page() {
  const searchParams = useSearchParams();

  const [error, setError] = useState("");

  const setAuth = useAuthStore((state) => state.setAuth);

  const router = useRouter();

  useEffect(() => {
    setError("");

    const token = searchParams.get("token");
    const id = searchParams.get("id");
    const refreshToken = searchParams.get("refreshToken");
    const email = searchParams.get("email");
    const firstName = searchParams.get("firstName");
    const lastName = searchParams.get("lastName");
    const expiresIn = searchParams.get("expiresIn");
    const refreshTokenExpiration = searchParams.get("refreshTokenExpiration");

    if (
      !token ||
      !id ||
      !refreshToken ||
      !email ||
      !firstName ||
      !lastName ||
      !expiresIn ||
      !refreshTokenExpiration
    ) {
      setError("Invalid parameters");
      return;
    } else {
      setAuth(
        {
          id,
          email,
          firstName,
          lastName,
        },
        token,
        Number(expiresIn),
        refreshToken,
      );
    }

    router.replace("/");
  }, [searchParams]);

  return (
    <div className="flex h-screen items-center justify-center">
      {error ? (
        <p className="py-12 text-center text-lg text-red-500 md:text-xl">
          {error}
        </p>
      ) : (
        <GenericPageLoading />
      )}
    </div>
  );
}
