"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card } from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import {
  Calendar as CalendarIcon,
  MapPin,
  Minus,
  Plus,
  Search,
  Users,
} from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";

const SearchForm = () => {
  const t = useTranslations();
  const locale = useLocale();
  const RTL = locale === "ar";

  const [selectedCity, setSelectedCity] = useState<string>("");
  const [checkIn, setCheckIn] = useState<Date>();
  const [checkOut, setCheckOut] = useState<Date>();
  const [guests, setGuests] = useState(1);

  // List of cities with Arabic support - you can expand this list as needed
  const cities = [
    { value: "riyadh", label: t("cities.riyadh") || "Riyadh" },
    { value: "jeddah", label: t("cities.jeddah") || "Jeddah" },
    { value: "makkah", label: t("cities.makkah") || "Makkah" },
    { value: "madinah", label: t("cities.madinah") || "Madinah" },
    { value: "dammam", label: t("cities.dammam") || "Dammam" },
    { value: "khobar", label: t("cities.khobar") || "Khobar" },
    { value: "taif", label: t("cities.taif") || "Taif" },
    { value: "abha", label: t("cities.abha") || "Abha" },
    { value: "tabuk", label: t("cities.tabuk") || "Tabuk" },
    { value: "hail", label: t("cities.hail") || "Hail" },
    { value: "dubai", label: t("cities.dubai") || "Dubai" },
    { value: "abu-dhabi", label: t("cities.abuDhabi") || "Abu Dhabi" },
    { value: "sharjah", label: t("cities.sharjah") || "Sharjah" },
    { value: "doha", label: t("cities.doha") || "Doha" },
    { value: "kuwait", label: t("cities.kuwait") || "Kuwait" },
    { value: "manama", label: t("cities.manama") || "Manama" },
    { value: "muscat", label: t("cities.muscat") || "Muscat" },
    { value: "cairo", label: t("cities.cairo") || "Cairo" },
    { value: "casablanca", label: t("cities.casablanca") || "Casablanca" },
    { value: "istanbul", label: t("cities.istanbul") || "Istanbul" },
  ];

  return (
    <Card className="bg-background mx-auto w-full max-w-4xl overflow-hidden rounded-2xl border-0 shadow-xl">
      <div className="p-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
          {/* City Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              {t("search.city") || "CITY"}
            </label>
            <Select value={selectedCity} onValueChange={setSelectedCity}>
              <SelectTrigger className="h-12 w-full px-4">
                <div className="flex items-center">
                  <MapPin className="mr-2 h-4 w-4 text-gray-500" />
                  <SelectValue
                    placeholder={t("search.selectCity") || "Select city"}
                  />
                </div>
              </SelectTrigger>
              <SelectContent>
                {cities.map((city) => (
                  <SelectItem key={city.value} value={city.value}>
                    {city.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {/* Check-in Date */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              {t("search.checkIn")}
            </label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "h-12 w-full justify-start px-4 text-left font-normal",
                    !checkIn && "text-gray-500",
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {checkIn
                    ? format(checkIn, "MMM dd, yyyy")
                    : t("search.selectDate")}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={checkIn}
                  onSelect={(date) => date && setCheckIn(date as Date)}
                  disabled={(date) => date < new Date()}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Check-out Date */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              {t("search.checkOut")}
            </label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "h-12 w-full justify-start px-4 text-left font-normal",
                    !checkOut && "text-gray-500",
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {checkOut
                    ? format(checkOut, "MMM dd, yyyy")
                    : t("search.selectDate")}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={checkOut}
                  onSelect={(date) => date && setCheckOut(date as Date)}
                  disabled={(date) =>
                    date < new Date() || (checkIn && date <= checkIn)
                  }
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Guests */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              {t("search.guests")}
            </label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="h-12 w-full justify-start px-4 text-left font-normal"
                >
                  <Users className="mr-2 h-4 w-4" />
                  {guests}{" "}
                  {guests === 1 ? t("search.guest") : t("search.guestsPlural")}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4" align="start">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">
                    {t("search.guests")}
                  </h4>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">
                      {t("search.guests")}
                    </span>
                    <div className="flex items-center space-x-3">
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-8 w-8 rounded-full"
                        onClick={() => setGuests(Math.max(1, guests - 1))}
                        disabled={guests <= 1}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <span className="w-8 text-center font-medium">
                        {guests}
                      </span>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-8 w-8 rounded-full"
                        onClick={() => setGuests(Math.min(10, guests + 1))}
                        disabled={guests >= 10}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* Search Button */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 opacity-0">
              &nbsp;
            </label>
            <Button
              className="h-12 w-full rounded-lg bg-[#3ca8cc] font-medium text-white transition-colors hover:bg-[#2d8bb3]"
              onClick={() => {
                // Handle search logic here
                console.log({ selectedCity, checkIn, checkOut, guests });
              }}
            >
              <Search className="mr-2 h-4 w-4" />
              {t("search.search")}
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default SearchForm;
