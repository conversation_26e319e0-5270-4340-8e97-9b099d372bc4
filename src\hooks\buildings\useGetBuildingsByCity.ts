import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { getBuildingsByCity } from "@/api/buildings";

interface UseGetBuildingsParams {
  pageNumber?: number;
  pageSize?: number;
  cityId?: number;
}

export default function useGetBuildingsByCity({
  pageNumber,
  pageSize,
  cityId,
}: UseGetBuildingsParams) {
  const searchParams = useSearchParams();

  const resolvedCityId = cityId ?? Number(searchParams.get("cityId"));
  const resolvedPageNumber =
    pageNumber ?? (Number(searchParams.get("pageNumber")) || 1);
  const resolvedPageSize =
    pageSize ?? (Number(searchParams.get("pageSize")) || 10);

  return useQuery({
    queryKey: [
      "buildings",
      resolvedPageNumber,
      resolvedPageSize,
      resolvedCityId,
    ],
    queryFn: () =>
      getBuildingsByCity({
        cityId: resolvedCityId,
        pageNumber: resolvedPageNumber,
        pageSize: resolvedPageSize,
      }),
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 3,
    retryDelay: 1000,
  });
}
