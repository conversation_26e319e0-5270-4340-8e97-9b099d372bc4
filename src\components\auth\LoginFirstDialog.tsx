"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Mail } from "lucide-react";
import { useTranslations } from "next-intl";
import { Button } from "../ui/button";
import { useAuthDialogStore } from "@/store/authDialogStore";

export default function LoginFirstDialog({
  open,
  onOpenChangeAction,
}: {
  open: boolean;
  onOpenChangeAction: (open: boolean) => void;
}) {
  const t = useTranslations("auth");

  const openAuthDialog = useAuthDialogStore((state) => state.openDialog);

  return (
    <Dialog open={open} onOpenChange={onOpenChangeAction}>
      <DialogContent className="p-8 sm:max-w-[480px]">
        <div className="flex min-h-[300px] flex-col items-center justify-center gap-4">
          <div className="grid aspect-square place-content-center rounded-full bg-green-500/30 p-4 text-4xl">
            <Mail className="h-12 w-12 text-green-500" />
          </div>
          <DialogHeader>
            <DialogTitle asChild>
              <h2 className="text-center text-2xl font-bold md:text-3xl">
                {t("loginFirstDialog.title")}
              </h2>
            </DialogTitle>
            <DialogDescription asChild>
              <p className="text-muted-foreground text-center text-base md:text-lg">
                {t("loginFirstDialog.description")}
              </p>
            </DialogDescription>
          </DialogHeader>
        </div>

        <Button
          className="w-full"
          onClick={() => {
            openAuthDialog("signin");
            onOpenChangeAction(false);
          }}
        >
          {t("loginFirstDialog.loginNow")}
        </Button>
      </DialogContent>
    </Dialog>
  );
}
