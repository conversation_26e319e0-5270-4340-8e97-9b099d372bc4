"use client";

import { forgetPassword<PERSON><PERSON> } from "@/api/auth";
import { But<PERSON> } from "@/components/ui/button";
import { useAuthDialogStore } from "@/store/authDialogStore";
import { zodResolver } from "@hookform/resolvers/zod";
import { isAxiosError } from "axios";
import { MailIcon } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import FormFeedback from "../FormFeedback";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import EmailInput from "../ui/email-input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";

export default function ResetPasswordForm() {
  const t = useTranslations();
  const locale = useLocale();
  const isRTL = locale === "ar";

  const [isSubmitting, setIsSubmitting] = useState(false);
  const setAuthDialogType = useAuthDialogStore((state) => state.setAuthType);
  const closeAuthDialog = useAuthDialogStore((state) => state.closeDialog);
  const [showFeedback, setShowFeedback] = useState(false);

  // Create forgot password validation schema
  const forgotPasswordSchema = z.object({
    email: z
      .string()
      .min(1, t("validations.required"))
      .email(t("validations.email")),
  });

  const form = useForm<z.infer<typeof forgotPasswordSchema>>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof forgotPasswordSchema>) => {
    setIsSubmitting(true);

    try {
      await forgetPasswordApi({ email: values.email });

      // Show success feedback
      setShowFeedback(true);
      form.reset();
    } catch (error) {
      console.error(error);

      if (error && isAxiosError(error)) {
        if (error.response?.data.status === 404) {
          form.setError("root", {
            message: t("auth.forgotPasswordEmailNotFound"),
          });
        } else {
          form.setError("root", {
            message: t("errors.generalError"),
          });
        }
      } else {
        form.setError("root", {
          message: t("errors.generalError"),
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="mb-2 text-2xl font-bold">
            {t("auth.forgotPasswordTitle")}
          </h2>
          <p className="text-muted-foreground">
            {t("auth.forgotPasswordDescription")}
          </p>
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            noValidate
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel>{t("auth.email")}</FormLabel>
                  <FormControl>
                    <EmailInput placeholder={t("auth.enterEmail")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.formState.errors.root?.message && (
              <Alert
                variant="destructive"
                className="border-red-500 bg-red-200/50"
              >
                <AlertTitle>{isRTL ? "خطأ" : "Error"}</AlertTitle>
                <AlertDescription>
                  {form.formState.errors.root?.message}
                </AlertDescription>
              </Alert>
            )}

            <div className="flex flex-col gap-3 pt-4">
              <Button
                type="submit"
                className="w-full bg-[#279fc7] text-white hover:bg-[#279fc7]/90"
                disabled={isSubmitting}
              >
                {isSubmitting
                  ? t("auth.sendingResetEmail")
                  : t("auth.sendResetEmail")}
              </Button>
            </div>
          </form>
        </Form>

        <div className="text-muted-foreground text-center text-sm">
          <p>
            {t("auth.rememberPassword")}{" "}
            <button
              type="button"
              onClick={() => {
                setAuthDialogType("signin");
              }}
              className="font-medium text-[#279fc7] hover:underline"
            >
              {t("auth.signIn")}
            </button>
          </p>
        </div>
      </div>

      <FormFeedback
        open={showFeedback}
        onOpenChange={(open) => {
          if (!open) {
            form.reset();
            setShowFeedback(false);
            closeAuthDialog();
          }
        }}
        icon={MailIcon}
        title={t("auth.forgetPasswordFeedback.title")}
        description={t("auth.forgetPasswordFeedback.description")}
      />
    </>
  );
}
