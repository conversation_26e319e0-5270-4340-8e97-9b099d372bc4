import api from "@/lib/axios";
import { errorThrower } from "@/lib/errorUtils";
import { Building } from "@/types/buildings";
import { PaginationData } from "@/types/global";

type GetBuildingsParams = {
  pageNumber?: number;
  pageSize?: number;
};

type GetBuildingsResponse = {
  items: Building[];
} & PaginationData;

export const getBuildings = async ({
  pageNumber = 1,
  pageSize = 10,
}: GetBuildingsParams) => {
  try {
    const res = await api.get<GetBuildingsResponse>(`/Buildings`, {
      params: {
        pageNumber,
        pageSize,
      },
    });

    return res.data;
  } catch (err) {
    console.error(err);
    errorThrower(err);
  }
};

export const getBuildingsByCity = async ({
  cityId,
  pageNumber = 1,
  pageSize = 10,
}: GetBuildingsParams & { cityId?: number }) => {
  try {
    const res = await api.get<GetBuildingsResponse>(`/Buildings/city`, {
      params: {
        cityId,
        pageNumber,
        pageSize,
      },
    });

    return res.data;
  } catch (err) {
    console.error(err);
    errorThrower(err);
  }
};

export const getSingleBuilding = async ({ id }: { id: string }) => {
  try {
    const res = await api.get<Building>(`/Buildings/${id}`);

    return res.data;
  } catch (err) {
    console.error(err);
    errorThrower(err);
  }
};
