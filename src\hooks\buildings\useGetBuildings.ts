import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { getBuildings } from "@/api/buildings";

interface UseGetBuildingsParams {
  pageNumber?: number;
  pageSize?: number;
}

export default function useGetBuildings({
  pageNumber,
  pageSize,
}: UseGetBuildingsParams) {
  const searchParams = useSearchParams();

  const resolvedPageNumber =
    pageNumber ?? (Number(searchParams.get("pageNumber")) || 1);
  const resolvedPageSize =
    pageSize ?? (Number(searchParams.get("pageSize")) || 10);

  return useQuery({
    queryKey: ["buildings", resolvedPageNumber, resolvedPageSize],
    queryFn: () =>
      getBuildings({
        pageNumber: resolvedPageNumber,
        pageSize: resolvedPageSize,
      }),
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 3,
    retryDelay: 1000,
  });
}
