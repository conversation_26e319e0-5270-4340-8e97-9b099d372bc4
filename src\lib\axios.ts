// lib/axios.ts
import axios, { AxiosError, InternalAxiosRequestConfig } from "axios";
import { scheduleTokenRefresh } from "./authManager";
import { BASE_API_URL } from "@/constants";

const api = axios.create({
  baseURL: BASE_API_URL,
  timeout: 50000,
});

api.interceptors.request.use((config: InternalAxiosRequestConfig) => {
  const token =
    typeof window !== "undefined" ? localStorage.getItem("token") : null;

  const refreshToken =
    typeof window !== "undefined" ? localStorage.getItem("refreshToken") : null;

  console.log("🔍 Tokens in localStorage:", {
    hasToken: !!token,
    hasRefreshToken: !!refreshToken,
    tokenLength: token?.length || 0,
  });

  if (token) {
    config.headers["Authorization"] = `Bearer ${token}`;
    console.log("🔑 Token added to request");
  } else {
    console.log("⚠️ No token found");
  }

  // Don't set Content-Type for FormData - let browser set it with boundary
  if (!config.headers["Content-Type"] && !(config.data instanceof FormData)) {
    config.headers["Content-Type"] = "application/json";
  }

  return config;
});

let isRefreshing = false;
let failedQueue: {
  resolve: (value: unknown) => void;
  reject: (reason?: any) => void;
}[] = [];

const processQueue = (error: Error | null, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

api.interceptors.response.use(
  (response) => {
    console.log("✅ Response interceptor - Success:", response.config.url);
    return response;
  },
  async (error: AxiosError) => {
    console.log(
      "❌ Response interceptor - Error:",
      error.config?.url,
      error.response?.status,
    );

    const originalRequest = error.config as InternalAxiosRequestConfig & {
      _retry?: boolean;
    };

    // Do not attempt to refresh if the failing request is the refresh request itself
    const isRefreshRequest = originalRequest?.url?.includes(
      "/Auth/refresh-token",
    );

    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !isRefreshRequest
    ) {
      if (isRefreshing) {
        return new Promise(function (resolve, reject) {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            if (originalRequest.headers) {
              originalRequest.headers["Authorization"] = "Bearer " + token;
            }
            return api(originalRequest);
          })
          .catch((err) => Promise.reject(err));
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        // Guard against SSR and ensure tokens exist
        if (typeof window === "undefined") {
          throw new Error("Cannot refresh token on server-side");
        }

        const token = localStorage.getItem("token");
        const refreshToken = localStorage.getItem("refreshToken");

        if (!refreshToken) {
          throw new Error("Missing refresh token");
        }

        console.log("🔄 Refreshing token...", token, refreshToken);

        const { data } = await api.post("/Auth/refresh-token", {
          token: token,
          refreshToken: refreshToken,
        });

        console.log("data", data);

        localStorage.setItem("token", data.token);
        localStorage.setItem("refreshToken", data.refreshToken);
        scheduleTokenRefresh(data.expiresIn);

        processQueue(null, data.token);
        isRefreshing = false;

        if (originalRequest.headers) {
          originalRequest.headers["Authorization"] = "Bearer " + data.token;
        }
        return api(originalRequest);
      } catch (err) {
        processQueue(err as Error, null);
        isRefreshing = false;
        console.log("❌ Token refresh failed:", err);
        return Promise.reject(err);
      }
    }

    return Promise.reject(error);
  },
);

export default api;
