"use client";

import React, { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Mail,
  User,
  Edit,
  Save,
  X,
  KeyRound,
  Loader2,
  Phone,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useProfile, useUpdateProfile } from "@/hooks/account";
import { useTranslations } from "next-intl";

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
}

export default function UserCard() {
  const t = useTranslations("account.profile");

  // API hooks
  const { profile, isLoading, error } = useProfile();
  const updateProfileMutation = useUpdateProfile();

  // Form data state
  const [formData, setFormData] = useState<FormData>({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
  });

  // Edit mode state
  const [isEditing, setIsEditing] = useState(false);
  const [originalData, setOriginalData] = useState<FormData>({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
  });

  // Update form data when profile is loaded
  useEffect(() => {
    if (profile) {
      const profileData = {
        firstName: profile.firstName || "",
        lastName: profile.lastName || "",
        email: profile.email || "",
        phoneNumber: profile.phoneNumber || "",
      };
      setFormData(profileData);
      setOriginalData(profileData);
    }
  }, [profile]);

  const handleEditProfile = () => {
    setIsEditing(true);
    setOriginalData({ ...formData });
  };

  const handleSaveProfile = async () => {
    try {
      // Only send editable fields (firstName, lastName)
      const updateData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
      };
      await updateProfileMutation.mutateAsync(updateData);
      setIsEditing(false);
      setOriginalData({ ...formData });
    } catch (error) {
      // Error is handled by the mutation hook
      console.error("Failed to update profile:", error);
    }
  };

  const handleCancelEdit = () => {
    // Revert to original values
    setFormData({ ...originalData });
    setIsEditing(false);
  };

  const handleInputChange = (
    field: keyof FormData,
    value: string | Date | null,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };
  // Show loading state
  if (isLoading) {
    return (
      <Card className="mt-14">
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">{t("loadingProfile")}</span>
        </div>
      </Card>
    );
  }

  // Show error state
  if (error) {
    return (
      <Card className="mt-14">
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <p className="text-destructive mb-2">{t("failedToLoadProfile")}</p>
          <p className="text-muted-foreground mb-4 text-sm">
            {error instanceof Error ? error.message : t("anErrorOccurred")}
          </p>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            size="sm"
          >
            {t("tryAgain")}
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card className="bg-background mt-14">
      <div className="flex flex-col px-6">
        {/* Header with Title and Edit Button */}
        <div className="flex items-center justify-between pb-4">
          <h2 className="text-foreground text-xl font-semibold">
            {t("basicInformation")}
          </h2>
          {!isEditing && (
            <Button
              onClick={handleEditProfile}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
              disabled={updateProfileMutation.isPending}
            >
              <Edit className="h-4 w-4" />
              {t("edit")}
            </Button>
          )}
        </div>

        {/* Input Fields */}
        <div className="flex flex-col gap-6 p-6">
          {/* First Name */}
          <div className="w-full space-y-2">
            <Label
              htmlFor="firstName"
              className="text-foreground text-sm font-medium"
            >
              <User className="mr-2 inline h-4 w-4" />
              {t("firstName")}
            </Label>
            <Input
              id="firstName"
              value={formData.firstName}
              onChange={(e) => handleInputChange("firstName", e.target.value)}
              disabled={!isEditing}
              className={cn(
                "transition-all duration-200",
                !isEditing && "bg-muted/50 cursor-not-allowed",
              )}
            />
          </div>

          {/* Last Name */}
          <div className="w-full space-y-2">
            <Label
              htmlFor="lastName"
              className="text-foreground text-sm font-medium"
            >
              <User className="mr-2 inline h-4 w-4" />
              {t("lastName")}
            </Label>
            <Input
              id="lastName"
              value={formData.lastName}
              onChange={(e) => handleInputChange("lastName", e.target.value)}
              disabled={!isEditing}
              className={cn(
                "transition-all duration-200",
                !isEditing && "bg-muted/50 cursor-not-allowed",
              )}
            />
          </div>

          {/* Phone Number */}
          <div className="w-full space-y-2">
            <Label
              htmlFor="phoneNumber"
              className="text-foreground text-sm font-medium"
            >
              <Phone className="mr-2 inline h-4 w-4" />
              {t("phoneNumber")}
            </Label>
            <Input
              id="phoneNumber"
              type="number"
              value={formData.phoneNumber}
              disabled={true}
              className="bg-muted/50 cursor-not-allowed transition-all duration-200"
              placeholder={formData.phoneNumber}
            />
          </div>

          {/* Email Address */}
          <div className="w-full space-y-2">
            <Label
              htmlFor="email"
              className="text-foreground text-sm font-medium"
            >
              <Mail className="mr-2 inline h-4 w-4" />
              {t("emailAddress")}
            </Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              disabled={true}
              className="bg-muted/50 cursor-not-allowed transition-all duration-200"
            />
          </div>
        </div>

        {/* Action Buttons - Only show when editing */}
        {isEditing && (
          <div className="flex justify-between border-t pt-4">
            <div className="flex flex-wrap gap-3">
              <Button
                onClick={handleSaveProfile}
                className="flex items-center gap-2"
                disabled={updateProfileMutation.isPending}
              >
                {updateProfileMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                {updateProfileMutation.isPending ? t("saving") : t("save")}
              </Button>
              <Button
                onClick={handleCancelEdit}
                variant="outline"
                className="flex items-center gap-2"
                disabled={updateProfileMutation.isPending}
              >
                <X className="h-4 w-4" />
                {t("cancel")}
              </Button>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}
