import api from "@/lib/axios";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

export default function useCreateReview() {
  const t = useTranslations();

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createReview,
    onSuccess: () => {
      toast.success(t("testimonials.addReview.success.title"));
      queryClient.invalidateQueries({
        queryKey: ["total-reviews"],
      });
    },
    onError: () => {
      toast.error(t("testimonials.addReview.error.title"));
    },
  });
}

const createReview = async (data: { rating: number; comment: string }) => {
  try {
    const res = await api.post(`/TotalReviews`, data);
    return res.data;
  } catch (error) {
    throw error;
  }
};
