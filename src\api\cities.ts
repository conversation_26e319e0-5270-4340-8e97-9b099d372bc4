import api from "@/lib/axios";

const BASE_ROUTE = "/Buildings";

export interface Building {
  id: number;
  nameEn: string;
  nameAr: string;
  descriptionEn: string;
  descriptionAr: string;
  cityNameEn: string;
  cityNameAr: string;
  cityId: number;
  image: string;
  unitsCount: number;
}

export const citiesApi = {

 getAllBuildings : async (pageNumber: number, pageSize: number) => {
  const {data} =  await api.get(`${BASE_ROUTE}?pageNumber=${pageNumber}&pageSize=${pageSize}`);
  return data;
},

 getBuildingsByCity : async (
  cityId: number,
): Promise<Building[]> => {
  const {data} =  await api.get(`${BASE_ROUTE}/city?cityId=${cityId}`);
  return data;
},

 getBuildingById : async (id: number): Promise<Building> => {
  const {data} =  await api.get(`${BASE_ROUTE}/${id}`);
  return data;
},

 createBuilding : async (
  building: FormData,
): Promise<Building> => {
  const {data} =  await api.post(`${BASE_ROUTE}`, building);
  return data;
},

 updateBuilding : async (
  id: number,
  building: Partial<Building>,
  token: string,
): Promise<Building> => {
  const response = await api.put(`${BASE_ROUTE}/${id}`, building, {
    headers: { Authorization: `Bearer ${token}` },
  });
  return response.data;
},

 deleteBuilding = async (
  id: number,
  token: string,
): Promise<void> => {
  await api.delete(`${BASE_ROUTE}/${id}`, {
    headers: { Authorization: `Bearer ${token}` },
  });
},

 updateBuildingImage = async (
  id: number,
  imageFile: File,
  token: string,
): Promise<void> => {
  const formData = new FormData();
  formData.append("image", imageFile);

  await api.put(`${BASE_ROUTE}/${id}/image`, formData, {
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "multipart/form-data",
    },
  });
}
}