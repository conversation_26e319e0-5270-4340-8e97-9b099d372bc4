import heroImage from "@/../public/assets/9d8726ef-7525-46ab-9aa0-96b450e01622.jpg";
import { Award, Star, Users } from "lucide-react";
import { getTranslations } from "next-intl/server";
import Image from "next/image";
import SearchForm from "../SearchForm";
import { Badge } from "../ui/badge";

const HeroSection = async () => {
  const t = await getTranslations();

  return (
    <section className="relative flex min-h-screen items-center justify-center overflow-hidden py-16">
      <div className="absolute inset-0 z-0">
        <Image
          priority
          src={heroImage}
          alt="Modern luxury apartment interior with comfortable living space"
          className="h-full w-full transform object-cover transition-transform duration-75"
        />
        <div className="to-primary/20 absolute inset-0 bg-gradient-to-br from-black/30 via-black/20" />
      </div>

      {/* Content */}
      <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="animate-fade-in-up mb-12 text-center">
          <Badge className="mb-6 rounded-full border-white/30 bg-slate-600/30 px-6 py-2 text-sm font-semibold text-white transition-all duration-300 md:text-base">
            {t("hero.badge")}
          </Badge>
          <h1 className="mb-6 text-4xl leading-tight font-bold tracking-tight text-white md:text-6xl lg:text-7xl">
            {t("hero.title")}
            <br />
            <span className="to-brand-teal-light bg-gradient-to-r from-white bg-clip-text text-transparent">
              {t("hero.subtitle")}
            </span>
          </h1>
          <p className="mx-auto mb-8 max-w-2xl text-lg leading-relaxed text-white/90 md:text-xl">
            {t("hero.description")}
          </p>

          {/* Trust Indicators */}
          <div className="mb-8 flex items-center justify-center space-x-8 text-white/80">
            <div className="flex items-center space-x-2">
              <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
              <span className="text-sm font-medium">
                {t("hero.trustIndicators.rating")}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span className="text-sm font-medium">
                {t("hero.trustIndicators.happyGuests")}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="h-5 w-5" />
              <span className="text-sm font-medium">
                {t("hero.trustIndicators.awardWinning")}
              </span>
            </div>
          </div>
        </div>

        {/* Search Form */}
        <div className="animate-fade-in-up mx-auto max-w-4xl">
          <SearchForm />
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="from-background absolute right-0 bottom-0 left-0 z-5 h-32 bg-gradient-to-t to-transparent" />
    </section>
  );
};

export default HeroSection;
