"use client";

import { useState, useEffect } from "react";
import { ChevronUp } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const ScrollToTopButton = () => {
  const [isVisible, setIsVisible] = useState(false);

  // Show button when page is scrolled up to given distance
  const toggleVisibility = () => {
    if (window.pageYOffset > 300) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  };

  // Set scroll event listener
  useEffect(() => {
    window.addEventListener("scrollend", toggleVisibility);
    return () => {
      window.removeEventListener("scrollend", toggleVisibility);
    };
  }, []);

  // Scroll to top smoothly
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <Button
      onClick={scrollToTop}
      className={cn(
        "bg-primary hover:bg-primary/90 border-primary/20 fixed right-8 z-50 flex size-10 transform items-center justify-center rounded-full border shadow-lg transition-all duration-300 ease-in-out hover:scale-110 hover:shadow-xl",
        {
          "-bottom-12 opacity-0": !isVisible,
          "bottom-8 opacity-100": isVisible,
        },
      )}
      size="icon"
      aria-label="Scroll to top"
    >
      <ChevronUp className="text-primary-foreground h-6 w-6" />
    </Button>
  );
};

export default ScrollToTopButton;
