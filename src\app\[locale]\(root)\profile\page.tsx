import React from "react";
import ProfileInfo from "./ProfileInfo";
import Preferences from "./Preferences";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import Activation from "./Activation";
import { useLocale, useTranslations } from "next-intl";

export default function page() {
  const t = useTranslations("account.profile");
  const locale = useLocale();
  const isRTL = locale === "ar";

  return (
    <div className="mx-auto flex max-w-4xl flex-col items-center gap-6 p-4 sm:p-12">
      <Tabs
        dir={isRTL ? "rtl" : "ltr"}
        defaultValue="profile"
        className="w-full"
      >
        <TabsList className="mb-8 flex w-full justify-center sm:h-12">
          <TabsTrigger value="profile">{t("title")}</TabsTrigger>
          <TabsTrigger value="activation">{t("activation")}</TabsTrigger>
          <TabsTrigger value="preferences">{t("preferences")}</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <ProfileInfo />
        </TabsContent>

        <TabsContent value="activation">
          <Activation />
        </TabsContent>

        <TabsContent value="preferences">
          <Preferences />
        </TabsContent>
      </Tabs>
    </div>
  );
}
