import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, HelpCircle, MessageCircle } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";

const FAQSection = () => {
  const locale = useLocale();
  const RTL = locale === "ar";
  const t = useTranslations();

  const faqs = [
    {
      id: "1",
      question: "How do I book a property on Hala?",
      answer: "Booking is simple! Use our search form to find your perfect accommodation, select your dates and number of guests, then click 'Book Now'. You'll be guided through our secure payment process."
    },
    {
      id: "2",
      question: "What is included in the booking?",
      answer: "All our properties come with high-speed WiFi, 24/7 concierge support, professional cleaning, and premium amenities. Specific inclusions vary by property and are clearly listed in each property description."
    },
    {
      id: "3",
      question: "Can I cancel or modify my booking?",
      answer: "Yes! We offer flexible cancellation policies. Most bookings can be modified or cancelled up to 24-48 hours before check-in. Check your booking confirmation for specific terms."
    },
    {
      id: "4",
      question: "How do I access the property?",
      answer: "You'll receive detailed check-in instructions 24 hours before arrival, including keyless entry codes or key collection details. Our concierge team is available 24/7 to assist with any access issues."
    },
    {
      id: "5",
      question: "What if I need help during my stay?",
      answer: "Our 24/7 concierge team is always available via phone, chat, or our mobile app. We can help with everything from restaurant recommendations to emergency assistance."
    },
    {
      id: "6",
      question: "Are pets allowed?",
      answer: "Pet policies vary by property. Look for the pet-friendly icon when searching, or contact our team to find accommodations that welcome your furry friends."
    }
  ];

  return (
    <section className="py-24 bg-background relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-20 right-10 opacity-5">
        <HelpCircle className="w-32 h-32 text-primary" />
      </div>
      <div className="absolute bottom-20 left-10 opacity-5">
        <MessageCircle className="w-24 h-24 text-primary" />
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-primary/10 rounded-full px-4 py-2 mb-6">
            <HelpCircle className="w-4 h-4 text-primary mr-2" />
            <span className="text-primary text-sm font-semibold">Got Questions?</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
            Frequently Asked Questions
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Find answers to common questions about booking, stays, and our services.
          </p>
        </div>

        <Card className="border-0 shadow-soft bg-gradient-to-br from-background to-brand-warm/20">
          <CardContent className="p-8">
            <Accordion type="single" collapsible className="w-full space-y-4">
              {faqs.map((faq, index) => (
                <AccordionItem 
                  key={faq.id} 
                  value={faq.id}
                  className="border border-border/50 rounded-xl px-6 bg-background/50 hover:bg-background/80 transition-colors duration-300"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <AccordionTrigger className="text-left font-semibold text-foreground hover:text-primary transition-colors duration-300">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-muted-foreground leading-relaxed pt-2">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </CardContent>
        </Card>

        {/* Contact Support */}
        <div className="text-center mt-12">
          <div className="bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 rounded-2xl p-8">
            <MessageCircle className="w-12 h-12 text-primary mx-auto mb-4" />
            <h3 className="text-xl font-bold text-foreground mb-2">
              Still have questions?
            </h3>
            <p className="text-muted-foreground mb-6">
              Our support team is here to help you 24/7
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                className="bg-primary hover:bg-primary/90 text-white shadow-soft hover:shadow-elevated transition-all duration-300"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Live Chat
              </Button>
              <Button 
                variant="outline"
                className="border-primary text-primary hover:bg-primary hover:text-white transition-all duration-300"
              >
                Contact Support
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQSection;

