"use client";

import React, { useState, useRef, useEffect } from "react";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import {
  Camera,
  CheckCircle,
  Clock,
  XCircle,
  FileText,
  Loader2,
} from "lucide-react";
import { useProfile, useUpdateProfileImage } from "@/hooks/account";
import ImageCropDialog from "@/components/ImageCropDialog";
import { useLocale, useTranslations } from "next-intl";

export default function ImgCard() {
  const t = useTranslations("account.profile");
  const locale = useLocale();
  const { profileImg, profileStatus, profile, isLoading, error } = useProfile();
  const changeProfileImage = useUpdateProfileImage();
  const [profileImage, setProfileImage] = useState(
    "https://github.com/evilrabbit.png",
  );
  const [cropDialogOpen, setCropDialogOpen] = useState(false);
  const [selectedImageSrc, setSelectedImageSrc] = useState<string>("");
  const [imageLoading, setImageLoading] = useState<boolean>(true);

  // Status configuration object with icons, colors, and descriptions
  const statusConfig = {
    0: {
      icon: CheckCircle,
      bgColor: "bg-orange-500",
      textColor: "text-orange-100",
      description: locale === "ar" ? "يرجى إضافة الهوية" : "Verify ID",
    },
    1: {
      icon: Clock,
      bgColor: "bg-yellow-500",
      textColor: "text-yellow-100",
      description: locale === "ar" ? "قيد المراجعة" : "Under Review",
    },
    2: {
      icon: CheckCircle,
      bgColor: "bg-green-500",
      textColor: "text-green-100",
      description: locale === "ar" ? "تم الموافقة" : "Approved",
    },
    3: {
      icon: XCircle,
      bgColor: "bg-red-500",
      textColor: "text-red-100",
      description: locale === "ar" ? "تم الرفض" : "Rejected",
    },
  };

  // Get status configuration
  const getStatusConfig = (status: number) => {
    return (
      statusConfig[status as keyof typeof statusConfig] || {
        icon: FileText,
        bgColor: "bg-gray-500",
        textColor: "text-gray-100",
        description: "Unknown",
      }
    );
  };

  useEffect(() => {
    if (profileImg) {
      setImageLoading(true);
      setProfileImage(profileImg);
    }
  }, [profileImg]);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageSrc = e.target?.result as string;
        setSelectedImageSrc(imageSrc);
        setCropDialogOpen(true);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCropComplete = (croppedImageBlob: Blob) => {
    // Convert blob to file
    const croppedFile = new File(
      [croppedImageBlob],
      "cropped-profile-image.jpg",
      {
        type: "image/jpeg",
      },
    );

    // Update the profile image preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setProfileImage(e.target?.result as string);
    };
    reader.readAsDataURL(croppedFile);
    changeProfileImage.mutate(croppedFile);
  };

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="relative mb-8 md:mb-0">
      {/* Solid Background Container */}
      <div className="relative h-32 w-full overflow-hidden rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 opacity-50" />
      </div>

      {/* Profile Avatar - Centered with Click Action */}
      <div className="absolute bottom-0 left-0 flex w-full translate-y-1/2 transform flex-col items-center justify-between place-self-center px-4 md:translate-y-1/4 md:flex-row">
        <div className="text-primary-foreground flex flex-col items-center gap-0 md:flex-row md:gap-2">
          <div
            className="group relative cursor-pointer"
            onClick={handleImageClick}
          >
            <Avatar className="border-primary h-30 w-30 border-4 shadow-lg">
              <AvatarImage
                src={profileImage}
                alt={t("profilePicture")}
                onLoad={() => setImageLoading(false)}
                onError={() => setImageLoading(false)}
                className={
                  imageLoading
                    ? "opacity-0"
                    : "opacity-100 transition-opacity duration-300"
                }
              />
              {imageLoading && (
                <div className="absolute inset-0 flex items-center justify-center rounded-full bg-gray-200">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                </div>
              )}
            </Avatar>
            {/* Overlay on hover */}
            <div className="absolute inset-0 flex items-center justify-center rounded-full bg-black/40 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
              <Camera className="h-6 w-6 text-white" />
            </div>
          </div>

          <div className="text-primary text-lg font-bold md:text-white">
            {profile?.firstName} {profile?.lastName}
          </div>
        </div>

        <div>
          <div
            className={`flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${getStatusConfig(profileStatus!).bgColor} ${getStatusConfig(profileStatus!).textColor}`}
          >
            {React.createElement(getStatusConfig(profileStatus!).icon, {
              className: "h-3 w-3",
            })}
            {getStatusConfig(profileStatus!).description}
          </div>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        title={t("uploadProfilePicture")}
        placeholder={t("chooseImage")}
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageChange}
        className="hidden"
      />

      {/* Image Crop Dialog */}
      <ImageCropDialog
        isOpen={cropDialogOpen}
        onClose={() => setCropDialogOpen(false)}
        imageSrc={selectedImageSrc}
        onCropComplete={handleCropComplete}
      />
    </div>
  );
}
