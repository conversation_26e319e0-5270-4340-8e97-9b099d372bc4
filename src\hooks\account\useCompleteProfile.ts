import { accountApi } from "@/api/account";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export interface CompleteProfileRequest {
  birthDateGregorian: string;
  birthDateHijri: string;
  nationality: string;
  files?: File[];
  onUploadProgress?: (e: ProgressEvent) => void;
}

export const useCompleteProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CompleteProfileRequest) =>
      accountApi.completeProfile(
        data.birthDateGregorian,
        data.birthDateHijri,
        data.nationality,
        data.files,
        data.onUploadProgress,
      ),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["profile"] });
      toast.success("Profile completed successfully");
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to complete profile",
      );
    },
  });
};
