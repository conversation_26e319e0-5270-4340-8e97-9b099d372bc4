"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  ArrowRight,
  Car,
  Clock,
  DollarSign,
  Home,
  Plane,
  Shield,
  Star,
  Tag,
  UtensilsCrossed,
  Wrench
} from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { useRouter } from "next/navigation";

// Import images from shared constants
import {
  image1,
  image2,
  image3,
  image4,
  image5,
  image6
} from "@/constants/images";
import Image from "next/image";

const FeaturedSections = () => {
  const t = useTranslations();
  const locale = useLocale();
  const isRTL = locale === "ar";

  const router = useRouter();

  const navigate = (route: string) => {
    router.push(route);
  };

  // Sample units data (text will be localized via i18n)
  const units = [
    {
      id: 1,
      title: "شقة فاخرة في المركز",
      image: image1,
      price: "2,500 ر.س/شهر",
      rating: 4.8,
      bedrooms: 2,
      bathrooms: 2,
      area: 120,
    },
    {
      id: 2,
      title: "استوديو حديث",
      image: image2,
      price: "1,800 ر.س/شهر",
      rating: 4.6,
      bedrooms: 1,
      bathrooms: 1,
      area: 65,
    },
    {
      id: 3,
      title: "بنتهاوس مع إطلالة",
      image: image3,
      price: "4,200 ر.س/شهر",
      rating: 4.9,
      bedrooms: 3,
      bathrooms: 3,
      area: 180,
    },
    {
      id: 4,
      title: "فيلا عصرية",
      image: image4,
      price: "5,800 ر.س/شهر",
      rating: 4.9,
      bedrooms: 4,
      bathrooms: 3,
      area: 250,
    },
    {
      id: 5,
      title: "شقة عائلية واسعة",
      image: image5,
      price: "3,200 ر.س/شهر",
      rating: 4.7,
      bedrooms: 3,
      bathrooms: 2,
      area: 140,
    },
    {
      id: 6,
      title: "لوفت مع تراس",
      image: image6,
      price: "2,900 ر.س/شهر",
      rating: 4.8,
      bedrooms: 2,
      bathrooms: 2,
      area: 110,
    },
  ];

  // Services data
  const services = [
    {
      id: 1,
      name: "خدمة النظافة",
      icon: Shield,
      description: "تنظيف شامل للوحدة",
      price: "150 ر.س",
    },
    {
      id: 2,
      name: "الصيانة",
      icon: Wrench,
      description: "صيانة فورية 24/7",
      price: "من 100 ر.س",
    },
    {
      id: 3,
      name: "خدمة التوصيل",
      icon: Car,
      description: "توصيل من وإلى المطار",
      price: "80 ر.س",
    },
    {
      id: 4,
      name: "طلب الوجبات",
      icon: UtensilsCrossed,
      description: "وجبات محضرة طازجة",
      price: "50 ر.س",
    },
    {
      id: 5,
      name: "حجز التاكسي",
      icon: Plane,
      description: "مواصلات سريعة ومريحة",
      price: "25 ر.س",
    },
  ];

  // Special offers data (text will be localized via i18n)
  const offers = [
    {
      id: 1,
      title: "عرض الإقامة الطويلة",
      description: "خصم 20% على الإقامة أكثر من 30 يوم",
      discount: "20%",
      validUntil: "31 ديسمبر 2024",
      type: "limited",
    },
    {
      id: 2,
      title: "باقة الخدمات الشاملة",
      description: "نظافة + صيانة + وجبات بسعر مخفض",
      discount: "300 ر.س",
      originalPrice: "500 ر.س",
      type: "bundle",
    },
    {
      id: 3,
      title: "عرض العملاء الجدد",
      description: "أول إقامة بخصم 15%",
      discount: "15%",
      validUntil: "نهاية الشهر",
      type: "new-customer",
    },
  ];

  return (
    <div id="properties" className="space-y-24 py-16">
      {/* Units Section */}
      <section className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <div className="bg-primary/10 mb-6 inline-flex items-center rounded-full px-4 py-2">
            <Home className="text-primary mr-2 h-4 w-4" />
            <span className="text-primary text-sm font-semibold">
              {t("featured.unitsSection.badge", "الوحدات المتاحة")}
            </span>
          </div>
          <h2 className="text-foreground mb-6 text-3xl leading-tight font-bold md:text-4xl lg:text-5xl">
            {t("featured.unitsSection.title", "وحدات مميزة للإيجار")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-3xl text-lg leading-relaxed">
            {t(
              "featured.unitsSection.description",
              "اختر من مجموعة متنوعة من الوحدات السكنية المجهزة بالكامل",
            )}
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {units.map((unit, index) => (
            <Card
              key={unit.id}
              className="group shadow-soft hover:shadow-elevated transform cursor-pointer overflow-hidden border-0 transition-all duration-500 hover:-translate-y-2"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="relative overflow-hidden">
                <Image
                  src={unit.image}
                  alt={t(`featured.units.${unit.id}.title`, unit.title)}
                  className="h-64 w-full object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

                {/* Rating */}
                <div className="absolute top-4 right-4 rounded-lg bg-white/20 px-2 py-1 backdrop-blur-sm">
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm font-semibold text-white">
                      {unit.rating}
                    </span>
                  </div>
                </div>

                {/* Price */}
                <div className="absolute top-4 left-4">
                  <Badge className="bg-primary px-3 py-2 font-bold text-white">
                    {t(`featured.units.${unit.id}.price`, unit.price)}
                  </Badge>
                </div>

                {/* Unit details overlay */}
                <div className="absolute right-4 bottom-4 left-4 text-white">
                  <h3 className="mb-2 text-xl font-bold">
                    {t(`featured.units.${unit.id}.title`, unit.title)}
                  </h3>
                  <div className="flex items-center space-x-4 text-sm">
                    <span>{t("featured.unitsSection.bedrooms")}</span>
                    <span>
                      {t("featured.unitsSection.bathrooms", {
                        count: unit.bathrooms,
                        defaultValue: `${unit.bathrooms} حمام`,
                      })}
                    </span>
                    <span>
                      {unit.area} {t("featured.unitsSection.sqm", "م²")}
                    </span>
                  </div>
                </div>
              </div>

              <CardContent className="p-6">
                <Button
                  variant="ghost"
                  onClick={() => navigate(`/property/${unit.id}`)}
                  className="group-hover:bg-primary text-primary border-primary/20 hover:border-primary w-full justify-between border font-semibold transition-all duration-300 group-hover:text-white"
                >
                  <span>
                    {t("featured.unitsSection.viewDetails", "عرض التفاصيل")}
                  </span>
                  <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Special Offers Section */}
      <section className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <div className="mb-6 inline-flex items-center rounded-full bg-orange-100 px-4 py-2">
            <Tag className="mr-2 h-4 w-4 text-orange-600" />
            <span className="text-sm font-semibold text-orange-600">
              {t("featured.offersSection.badge", "العروض الخاصة")}
            </span>
          </div>
          <h2 className="text-foreground mb-6 text-3xl leading-tight font-bold md:text-4xl lg:text-5xl">
            {t("featured.offersSection.title", "عروض وخصومات حصرية")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-3xl text-lg leading-relaxed">
            {t(
              "featured.offersSection.description",
              "اغتنم الفرصة واحجز الآن بأفضل الأسعار",
            )}
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {offers.map((offer, index) => (
            <Card
              key={offer.id}
              className={`group transform cursor-pointer overflow-hidden border-2 transition-all duration-500 hover:-translate-y-2 ${
                offer.type === "limited"
                  ? "border-red-200 bg-red-50"
                  : offer.type === "bundle"
                    ? "border-green-200 bg-green-50"
                    : "border-blue-200 bg-blue-50"
              }`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardContent className="p-6">
                {/* Discount Badge */}
                <div className="mb-4 flex items-start justify-between">
                  <Badge
                    className={`px-3 py-2 text-lg font-bold text-white ${
                      offer.type === "limited"
                        ? "bg-red-500"
                        : offer.type === "bundle"
                          ? "bg-green-500"
                          : "bg-blue-500"
                    }`}
                  >
                    {t("featured.offersSection.discount", "خصم")}{" "}
                    {offer.discount}
                  </Badge>
                  {offer.type === "limited" && (
                    <div className="flex items-center text-xs text-red-600">
                      <Clock className="mr-1 h-3 w-3" />
                      <span>
                        {t("featured.offersSection.limitedOffer", "عرض محدود")}
                      </span>
                    </div>
                  )}
                </div>

                <h3 className="mb-3 text-xl font-bold">
                  {t(`featured.offers.${offer.id}.title`, offer.title)}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {t(
                    `featured.offers.${offer.id}.description`,
                    offer.description,
                  )}
                </p>

                {offer.originalPrice && (
                  <div className="mb-4 flex items-center space-x-2">
                    <span className="text-muted-foreground line-through">
                      {t(
                        `featured.offers.${offer.id}.originalPrice`,
                        offer.originalPrice,
                      )}
                    </span>
                    <DollarSign className="h-4 w-4 text-green-600" />
                  </div>
                )}

                {offer.validUntil && (
                  <div className="text-muted-foreground mb-4 flex items-center text-sm">
                    <Clock className="mr-2 h-4 w-4" />
                    <span>
                      {t("featured.offersSection.validUntil", "ساري حتى")}{" "}
                      {t(
                        `featured.offers.${offer.id}.validUntil`,
                        offer.validUntil,
                      )}
                    </span>
                  </div>
                )}

                <Button
                  className={`w-full transition-all duration-300 group-hover:shadow-lg ${
                    offer.type === "limited"
                      ? "bg-red-500 hover:bg-red-600"
                      : offer.type === "bundle"
                        ? "bg-green-500 hover:bg-green-600"
                        : "bg-blue-500 hover:bg-blue-600"
                  }`}
                >
                  {t("featured.offersSection.bookNow", "احجز الآن")}
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
    </div>
  );
};

export default FeaturedSections;
