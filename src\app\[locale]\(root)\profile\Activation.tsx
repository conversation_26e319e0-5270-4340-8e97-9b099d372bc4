"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useProfile } from "@/hooks/account";
import { useCompleteProfile } from "@/hooks/account/useCompleteProfile";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import {
  CalendarIcon,
  Edit,
  FileText,
  Save,
  Upload,
  User,
  X,
} from "lucide-react";
import { useTranslations } from "next-intl";
import type React from "react";
import { useEffect, useState } from "react";
import AdminResponse from "./components/AdminResponse";

import moment from "moment-hijri";
moment.locale("en");

interface ActivationData {
  birthdateGregorian: Date | undefined;
  birthdateHijri: string;
  nationality: string;
  uploadedFiles: File[];
}

interface FileUpload {
  file: File;
  progress: number;
  uploaded: boolean;
}

export default function Activation() {
  const t = useTranslations("account.activation");
  const { profile, profileStatus, isLoading, error } = useProfile();

  const completeProfileMutation = useCompleteProfile();

  const moment = require("moment-hijri");
  moment.locale("en");

  // Initial data - will be populated from profile
  const getInitialData = (): ActivationData => ({
    birthdateGregorian: profile?.birthDateGregorian
      ? new Date(profile.birthDateGregorian)
      : undefined,
    birthdateHijri: profile?.birthDateHijri || "",
    nationality: profile?.nationality || "",
    uploadedFiles: [],
  });

  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<ActivationData>(getInitialData());
  const [originalData, setOriginalData] =
    useState<ActivationData>(getInitialData());
  const [fileUploads, setFileUploads] = useState<FileUpload[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [calendarDate, setCalendarDate] = useState<Date>(() => {
    const defaultDate = new Date();
    defaultDate.setFullYear(defaultDate.getFullYear() - 25); // Default to 25 years ago
    return defaultDate;
  });

  // Update form data when profile loads
  useEffect(() => {
    if (profile) {
      const profileData = getInitialData();
      setFormData(profileData);
      setOriginalData(profileData);
    }
  }, [profile]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    if (!profile?.id) return;

    const completeProfileData = {
      birthDateGregorian: formData.birthdateGregorian
        ? format(formData.birthdateGregorian, "yyyy-MM-dd")
        : "",
      birthDateHijri: formData.birthdateHijri,
      nationality: formData.nationality,
      files: formData.uploadedFiles,
    };

    completeProfileMutation.mutate(completeProfileData, {
      onSuccess: () => {
        setOriginalData(formData);
        setIsEditing(false);
      },
    });
  };

  const handleCancel = () => {
    setFormData(originalData);
    setIsEditing(false);
  };

  const handleInputChange = (field: keyof ActivationData, value: string) => {
    setFormData((prev) => {
      const newData = {
        ...prev,
        [field]: value,
      };

      // Auto-convert Gregorian to Hijri date
      if (field === "birthdateGregorian" && value) {
        try {
          const gregorianDate = moment(value, "YYYY-MM-DD");
          if (gregorianDate.isValid()) {
            const hijriDate = gregorianDate.format("iYYYY-iMM-iDD");
            newData.birthdateHijri = hijriDate;
          }
        } catch (error) {
          console.error("Error converting to Hijri date:", error);
        }
      }

      return newData;
    });
  };

  const handleFileUpload = (files: FileList | null) => {
    if (!files) return;

    const newFiles = Array.from(files).map((file) => ({
      file,
      progress: 0,
      uploaded: false,
    }));

    setFileUploads((prev) => [...prev, ...newFiles]);

    setFormData((prev) => ({
      ...prev,
      uploadedFiles: [...prev.uploadedFiles, ...newFiles.map((f) => f.file)],
    }));

    // Simulate upload progress
    newFiles.forEach((fileUpload, index) => {
      const interval = setInterval(() => {
        setFileUploads((prev) =>
          prev.map((upload) =>
            upload.file === fileUpload.file
              ? { ...upload, progress: Math.min(upload.progress + 10, 100) }
              : upload,
          ),
        );
      }, 200);

      setTimeout(() => {
        clearInterval(interval);
        setFileUploads((prev) =>
          prev.map((upload) =>
            upload.file === fileUpload.file
              ? { ...upload, progress: 100, uploaded: true }
              : upload,
          ),
        );
      }, 2000);
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileUpload(e.dataTransfer.files);
  };

  const removeFile = (fileToRemove: File) => {
    setFileUploads((prev) =>
      prev.filter((upload) => upload.file !== fileToRemove),
    );
    setFormData((prev) => ({
      ...prev,
      uploadedFiles: prev.uploadedFiles.filter((f) => f !== fileToRemove),
    }));
  };

  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const maxYear = currentYear - 18; // Users must be at least 18 years old
    const minYear = currentYear - 100; // Go back 100 years
    const years = [];
    for (let year = maxYear; year >= minYear; year--) {
      years.push(year);
    }
    return years;
  };

  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const handleYearChange = (year: string) => {
    const newDate = new Date(calendarDate);
    newDate.setFullYear(Number.parseInt(year));
    setCalendarDate(newDate);
  };

  const handleMonthChange = (month: string) => {
    const newDate = new Date(calendarDate);
    newDate.setMonth(Number.parseInt(month));
    setCalendarDate(newDate);
  };

  if (isLoading) {
    return (
      <Card className="md:col-span-2 lg:col-span-3">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="text-muted-foreground">{t("loadingProfile")}</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="md:col-span-2 lg:col-span-3">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="text-destructive">{t("errorLoadingProfile")}</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="flex w-full max-w-3xl flex-col gap-4 place-self-center p-6">
      <AdminResponse profileStatus={profileStatus!} />

      <Card className="bg-background md:col-span-2 lg:col-span-3">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-foreground text-xl font-semibold">
            {t("title")}
          </CardTitle>
          {profileStatus !== 2 &&
            profileStatus !== 1 &&
            (!isEditing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={handleEdit}
                className="hover:bg-primary/10 hover:text-primary hover:border-primary flex items-center gap-2 bg-transparent transition-colors"
              >
                <Edit className="h-4 w-4" />
                {t("edit")}
              </Button>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
                className="hover:bg-destructive/10 hover:text-destructive hover:border-destructive flex items-center gap-2 bg-transparent transition-colors"
              >
                <X className="h-4 w-4" />
                {t("cancel")}
              </Button>
            ))}
        </CardHeader>

        <CardContent className="flex flex-col gap-6 p-6">
          <div className="space-y-2">
            <Label
              htmlFor="birthdateGregorian"
              className="text-foreground text-sm font-medium"
            >
              <CalendarIcon className="mr-2 inline h-4 w-4" />
              {t("birthdateGregorian")}
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  disabled={!isEditing}
                  className={cn(
                    "w-full justify-start text-left font-normal transition-all duration-200",
                    !formData.birthdateGregorian && "text-muted-foreground",
                    !isEditing && "bg-muted/50 cursor-not-allowed opacity-70",
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData.birthdateGregorian
                    ? format(formData.birthdateGregorian, "MMM dd, yyyy")
                    : "Select birthdate"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <div className="flex gap-2 border-b p-3">
                  <Select
                    value={calendarDate.getMonth().toString()}
                    onValueChange={handleMonthChange}
                  >
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Month" />
                    </SelectTrigger>
                    <SelectContent>
                      {monthNames.map((month, index) => (
                        <SelectItem key={index} value={index.toString()}>
                          {month}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select
                    value={calendarDate.getFullYear().toString()}
                    onValueChange={handleYearChange}
                  >
                    <SelectTrigger className="w-[100px]">
                      <SelectValue placeholder="Year" />
                    </SelectTrigger>
                    <SelectContent>
                      {generateYearOptions().map((year) => (
                        <SelectItem key={year} value={year.toString()}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <Calendar
                  mode="single"
                  selected={formData.birthdateGregorian}
                  onSelect={(date) => {
                    setFormData((prev) => ({
                      ...prev,
                      birthdateGregorian: date,
                    }));
                    if (date) {
                      const hijriDate = moment(date).format("iYYYY-iMM-iDD");
                      setFormData((prev) => ({
                        ...prev,
                        birthdateHijri: hijriDate,
                      }));
                    }
                  }}
                  disabled={(date) => {
                    const currentYear = new Date().getFullYear();
                    const minDate = new Date(currentYear - 100, 0, 1);
                    const maxDate = new Date(currentYear - 18, 11, 31);
                    return date > maxDate || date < minDate;
                  }}
                  month={calendarDate}
                  onMonthChange={setCalendarDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="birthdateHijri"
              className="text-foreground text-sm font-medium"
            >
              <CalendarIcon className="mr-2 inline h-4 w-4" />
              {t("birthdateHijri")}
            </Label>

            <Input
              id="birthdateHijri"
              value={formData.birthdateHijri}
              disabled={true}
              placeholder="yyyy-mm-dd"
              className="bg-muted/50 cursor-not-allowed opacity-70 transition-all duration-200"
            />

            <div className="space-y-2">
              <Label
                htmlFor="nationality"
                className="text-foreground text-sm font-medium"
              >
                <User className="mr-2 inline h-4 w-4" />
                {t("nationality")}
              </Label>
              <Input
                id="nationality"
                value={formData.nationality}
                onChange={(e) =>
                  handleInputChange("nationality", e.target.value)
                }
                disabled={!isEditing}
                className={`transition-all duration-200 ${
                  !isEditing
                    ? "bg-muted/50 cursor-not-allowed opacity-70"
                    : "bg-background"
                }`}
              />
            </div>
          </div>

          {/* File Upload Section */}
          <div className="space-y-4">
            <h3 className="text-foreground text-lg font-semibold">
              {t("documentUpload")}
            </h3>

            {isEditing && (
              <div
                className={`rounded-lg border-2 border-dashed p-6 text-center transition-colors ${
                  isDragOver
                    ? "border-primary bg-primary/5"
                    : "border-muted-foreground/25 hover:border-primary/50"
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <Upload className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <p className="text-muted-foreground mb-2 text-sm">
                  {t("dragDropFiles")}
                </p>
                <input
                  aria-label="File upload input"
                  title="Choose files to upload"
                  placeholder="No file chosen"
                  type="file"
                  multiple
                  className="hidden"
                  id="file-upload"
                  onChange={(e) => handleFileUpload(e.target.files)}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() =>
                    document.getElementById("file-upload")?.click()
                  }
                  className="mt-2"
                >
                  <FileText className="mr-2 h-4 w-4" />
                  {t("chooseFiles")}
                </Button>
              </div>
            )}

            {/* File Upload Progress */}
            {fileUploads.length > 0 ? (
              <div className="space-y-3">
                <h4 className="text-foreground text-sm font-medium">
                  {t("uploadedFiles")}
                </h4>
                {fileUploads.map((upload, index) => (
                  <div key={index} className="bg-muted/30 rounded-lg p-4">
                    <div className="mb-2 flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FileText className="text-muted-foreground h-4 w-4" />
                        <span className="truncate text-sm font-medium">
                          {upload.file.name}
                        </span>
                        <span className="text-muted-foreground text-xs">
                          ({(upload.file.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      {isEditing && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(upload.file)}
                          className="hover:bg-destructive/10 hover:text-destructive h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                    <div className="space-y-1">
                      <Progress value={upload.progress} className="h-2" />
                      <div className="text-muted-foreground flex justify-between text-xs">
                        <span>
                          {upload.uploaded ? t("completed") : t("uploading")}
                        </span>
                        <span>{upload.progress}%</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-muted/20 rounded-lg p-6 text-center">
                <FileText className="text-muted-foreground mx-auto mb-2 h-8 w-8" />
                <p className="text-muted-foreground mb-1 text-sm font-medium">
                  {t("noFilesUploaded")}
                </p>
                <p className="text-muted-foreground text-xs">
                  {isEditing
                    ? t("uploadDocumentsMessage")
                    : t("noDocumentsMessage")}
                </p>
              </div>
            )}
          </div>
        </CardContent>

        {isEditing && (
          <CardFooter>
            <Button
              onClick={handleSave}
              disabled={completeProfileMutation.isPending}
              className="bg-primary hover:bg-primary/90 flex w-full items-center justify-center gap-2 transition-colors"
            >
              <Save className="h-4 w-4" />
              {completeProfileMutation.isPending
                ? t("saving")
                : t("saveChanges")}
            </Button>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}
