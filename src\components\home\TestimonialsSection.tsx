import { Heart, Quote } from "lucide-react";
import { getTranslations } from "next-intl/server";
import CreateReviewButton from "./testimonials/CreateReviewButton";
import TestimonialList from "./testimonials/TestimonialList";

const TestimonialsSection = async () => {
  const t = await getTranslations();

  return (
    <section className="to-primary/10 relative overflow-hidden bg-gradient-to-br from-[#F1EEE6]/5 py-24">
      {/* Background decorations */}
      <div className="absolute top-20 right-20 opacity-10">
        <Quote className="text-primary h-32 w-32" />
      </div>
      <div className="absolute bottom-20 left-20 opacity-10">
        <Heart className="text-primary h-24 w-24" />
      </div>

      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-14 text-center">
          <div className="from-primary/10 via-primary/20 to-primary/10 border-primary/20 mb-6 inline-flex items-center gap-2 rounded-full border bg-gradient-to-r px-6 py-3 backdrop-blur-sm">
            <Heart className="text-primary h-4 w-4" />
            <span className="text-primary text-sm font-semibold">
              {t("testimonials.subHeading")}
            </span>
          </div>
          <h2 className="text-foreground mb-6 text-3xl leading-tight font-bold md:text-4xl lg:text-5xl">
            {t("testimonials.title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-3xl text-lg leading-relaxed">
            {t("testimonials.description")}
          </p>
        </div>

        <TestimonialList />

        <div className="flex">
          <CreateReviewButton className="mx-auto mt-16 h-12 rounded-full px-6 text-white" />
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
