import { BASE_API_URL, REVALIDATE_TIMES } from "@/constants";
import { Link } from "@/i18n/navigation";
import { City } from "@/types/cities";
import { ArrowRight, MapPin } from "lucide-react";
import { getLocale, getTranslations } from "next-intl/server";
import Image from "next/image";

const CitiesSection = async () => {
  const locale = await getLocale();
  const t = await getTranslations();

  let error = null;

  const data = await fetch(`${BASE_API_URL}/Cities?PageNumber=1&PageSize=16`, {
    next: {
      revalidate: REVALIDATE_TIMES.HOME_PAGE,
    },
  })
    .then((res) => res.json())
    .catch((err) => {
      console.error(err);
      error = locale === "ar" ? "حدث خطأ" : "An error occurred";
    });

  if (!data || data.items.length === 0 || error) return null;

  const cities: City[] = data.items;

  return (
    <section className="px-4 py-20 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="mb-16 text-center">
          <h2 className="text-primary mb-4 text-4xl font-bold md:text-5xl">
            {t("cities.title")}
          </h2>
          <p className="mx-auto max-w-3xl text-xl">{t("cities.description")}</p>
        </div>

        <div className="grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-6">
          {cities.map((city) => (
            <div key={city.id} className="group cursor-pointer">
              <div className="relative transform overflow-hidden rounded-2xl shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl">
                <div className="aspect-w-4 aspect-h-5">
                  <Image
                    alt={locale === "ar" ? city.nameAr : city.nameEn}
                    className="h-80 w-full object-cover transition-transform duration-500 group-hover:scale-110"
                    src={city.image}
                    width={500}
                    height={500}
                  />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-transparent to-transparent opacity-100 transition-opacity duration-300" />
                <div className="absolute right-0 bottom-0 left-0 flex h-full translate-y-4 transform flex-col justify-end p-6 py-10 text-white transition-transform duration-300 group-hover:translate-y-0">
                  <h3 className="mb-2 text-xl font-semibold">
                    {locale === "ar" ? city.nameAr : city.nameEn}
                  </h3>
                  <p className="mb-4 text-sm text-white/80">
                    {city.buildingCount}+ {t("cities.buildings")}
                  </p>
                  <div className="flex items-center gap-3 transition-all delay-100 duration-300">
                    <Link
                      href={`/buildings?cityId=${city.id}`}
                      className="group/button inline-flex transform items-center gap-2 rounded-full border border-white/30 bg-white/20 px-4 py-2.5 text-sm font-medium text-white shadow-lg backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:border-white/50 hover:bg-white/30 hover:shadow-xl active:scale-95"
                    >
                      <MapPin className="h-4 w-4 opacity-80" />
                      <span>{t("cities.viewUnits")}</span>
                      <ArrowRight className="h-4 w-4 transform transition-transform duration-300 group-hover/button:translate-x-1 rtl:rotate-180" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CitiesSection;
