"use client";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  HeadphonesIcon,
  Globe,
  ArrowRight,
  Clock,
  LucideIcon,
} from "lucide-react";
import Link from "next/link";

interface SupportFeature {
  icon: LucideIcon;
  title: string;
  description: string;
}

const SupportFeatures: React.FC = () => {
  const t = useTranslations();

  const supportFeatures: SupportFeature[] = [
    {
      icon: Clock,
      title: t("contact.support.availability.title"),
      description: t("contact.support.availability.description"),
    },
    {
      icon: Globe,
      title: t("contact.support.multilingual.title"),
      description: t("contact.support.multilingual.description"),
    },
    {
      icon: HeadphonesIcon,
      title: t("contact.support.dedicated.title"),
      description: t("contact.support.dedicated.description"),
    },
  ];

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-foreground mb-6 text-2xl font-bold">
          {t("contact.support.title")}
        </h3>
        <p className="text-muted-foreground mb-8 leading-relaxed">
          {t("contact.support.description")}
        </p>
      </div>

      <div className="space-y-6">
        {supportFeatures.map((feature, index) => (
          <div
            key={index}
            className="bg-background shadow-soft hover:shadow-elevated flex items-start space-x-4 rounded-2xl p-6 transition-all duration-300"
          >
            <div className="from-primary to-brand-ocean flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r">
              <feature.icon className="h-6 w-6 text-white" />
            </div>
            <div>
              <h4 className="text-foreground mb-2 text-lg font-semibold">
                {feature.title}
              </h4>
              <p className="text-muted-foreground leading-relaxed">
                {feature.description}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* CTA */}
      <Card className="shadow-soft from-primary/5 to-brand-ocean/5 overflow-hidden border-0 bg-gradient-to-br">
        <CardContent className="p-8 text-center">
          <h4 className="text-foreground mb-4 text-xl font-bold">
            {t("contact.cta.title")}
          </h4>
          <p className="text-muted-foreground mb-6">
            {t("contact.cta.description")}
          </p>
          <Link href="/faq">
            <Button
              variant="outline"
              className="border-primary text-primary hover:bg-primary border-2 font-semibold transition-all duration-300 hover:text-white"
            >
              {t("contact.cta.button")}
              <ArrowRight className="h-4 w-4 rtl:rotate-180" />
            </Button>
          </Link>
        </CardContent>
      </Card>
    </div>
  );
};

export default SupportFeatures;
