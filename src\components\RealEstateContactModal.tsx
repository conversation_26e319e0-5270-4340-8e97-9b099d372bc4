import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Mail,
  MapPin,
  MessageSquare,
  Phone,
  Send,
  User,
  X,
} from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";

interface RealEstateContactModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const RealEstateContactModal = ({
  isOpen,
  onClose,
}: RealEstateContactModalProps) => {
  const t = useTranslations();
  const locale = useLocale();
  const isRTL = locale === "ar";

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    mobile: "",
    propertyLocation: "",
    message: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));
      setIsSubmitted(true);

      // Reset form after 3 seconds and close modal
      setTimeout(() => {
        setIsSubmitted(false);
        setFormData({
          name: "",
          email: "",
          mobile: "",
          propertyLocation: "",
          message: "",
        });
        onClose();
      }, 3000);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setIsSubmitted(false);
      setFormData({
        name: "",
        email: "",
        mobile: "",
        propertyLocation: "",
        message: "",
      });
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto rounded-2xl border-0 bg-white shadow-2xl">
        <DialogHeader className="relative pb-6">
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 right-4 h-8 w-8 rounded-full hover:bg-gray-100"
            onClick={handleClose}
          >
            <X className="h-4 w-4" />
          </Button>
          <DialogTitle className="mb-2 text-center text-3xl font-bold text-gray-900">
            {t("realEstate.contactModal.title")}
          </DialogTitle>
          <DialogDescription className="text-center text-lg text-gray-600">
            {t("realEstate.contactModal.description")}
          </DialogDescription>
        </DialogHeader>

        {isSubmitted ? (
          <div className="py-12 text-center">
            <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
              <Send className="h-10 w-10 text-green-600" />
            </div>
            <h3 className="mb-3 text-2xl font-semibold text-gray-900">
              {t("realEstate.contactModal.successTitle")}
            </h3>
            <p className="text-lg text-gray-600">
              {t("realEstate.contactModal.successMessage")}
            </p>
          </div>
        ) : (
          <Card className="border-0 shadow-none">
            <CardContent className="p-0">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label
                    htmlFor="name"
                    className="flex items-center text-base font-medium text-gray-700"
                  >
                    <User
                      className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"} text-primary`}
                    />
                    {t("realEstate.contactModal.name")}{" "}
                    {t("realEstate.contactModal.required")}
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder={t("realEstate.contactModal.namePlaceholder")}
                    className="focus:border-primary h-12 rounded-xl border-2 border-gray-200 text-base transition-colors"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="email"
                    className="flex items-center text-base font-medium text-gray-700"
                  >
                    <Mail
                      className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"} text-primary`}
                    />
                    {t("realEstate.contactModal.email")}{" "}
                    {t("realEstate.contactModal.required")}
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder={t("realEstate.contactModal.emailPlaceholder")}
                    className="focus:border-primary h-12 rounded-xl border-2 border-gray-200 text-base transition-colors"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="mobile"
                    className="flex items-center text-base font-medium text-gray-700"
                  >
                    <Phone
                      className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"} text-primary`}
                    />
                    {t("realEstate.contactModal.mobile")}{" "}
                    {t("realEstate.contactModal.required")}
                  </Label>
                  <Input
                    id="mobile"
                    type="tel"
                    name="mobile"
                    value={formData.mobile}
                    onChange={handleInputChange}
                    placeholder={t("realEstate.contactModal.mobilePlaceholder")}
                    className="focus:border-primary h-12 rounded-xl border-2 border-gray-200 text-base transition-colors"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="propertyLocation"
                    className="flex items-center text-base font-medium text-gray-700"
                  >
                    <MapPin
                      className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"} text-primary`}
                    />
                    {t("realEstate.contactModal.propertyLocation")}{" "}
                    {t("realEstate.contactModal.required")}
                  </Label>
                  <Input
                    id="propertyLocation"
                    name="propertyLocation"
                    value={formData.propertyLocation}
                    onChange={handleInputChange}
                    placeholder={t(
                      "realEstate.contactModal.propertyLocationPlaceholder",
                    )}
                    className="focus:border-primary h-12 rounded-xl border-2 border-gray-200 text-base transition-colors"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="message"
                    className="flex items-center text-base font-medium text-gray-700"
                  >
                    <MessageSquare
                      className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"} text-primary`}
                    />
                    {t("realEstate.contactModal.message")}{" "}
                    {t("realEstate.contactModal.required")}
                  </Label>
                  <Textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    placeholder={t(
                      "realEstate.contactModal.messagePlaceholder",
                    )}
                    className="focus:border-primary min-h-[120px] resize-none rounded-xl border-2 border-gray-200 text-base transition-colors"
                    required
                  />
                </div>

                <div className="pt-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-primary hover:bg-primary/90 h-14 w-full rounded-xl text-lg font-semibold text-white shadow-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-xl"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center justify-center">
                        <div
                          className={`h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent ${isRTL ? "ml-2" : "mr-2"}`}
                        />
                        {t("realEstate.contactModal.sending")}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <Send
                          className={`h-5 w-5 ${isRTL ? "ml-2" : "mr-2"}`}
                        />
                        {t("realEstate.contactModal.sendMessage")}
                      </div>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default RealEstateContactModal;
