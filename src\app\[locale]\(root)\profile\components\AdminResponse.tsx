import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { <PERSON>ert<PERSON>rian<PERSON>, Clock, CheckCircle, XCircle } from "lucide-react";
import { useTranslations } from "next-intl";
import React from "react";

interface StatusConfig {
  statusCode: number;
  icon: React.ReactNode;
  background: string;
  borderColor: string;
  textColor: string;
  title: string;
  description: string;
}

export default function AdminResponse({
  profileStatus,
  rejectionReason,
}: {
  profileStatus: number;
  rejectionReason?: string;
}) {
  const t = useTranslations("account.adminResponse");

  const getStatusConfig = (status: number): StatusConfig | null => {
    switch (status) {
      case 0: // Current (User Action Required)
        return {
          statusCode: 0,
          icon: <AlertTriangle className={`mx-2 h-5 w-5`} />,
          background: "bg-amber-50 dark:bg-amber-950",
          borderColor: "border-amber-200 dark:border-amber-800",
          textColor: "text-amber-800 dark:text-amber-200",
          title: t("verification.needed"),
          description: t("verification.uploadDocuments"),
        };
      case 1: // Pending (Admin Review)
        return {
          statusCode: 1,
          icon: <Clock className={`mx-2 h-5 w-5`} />,
          background: "bg-blue-50 dark:bg-blue-950",
          borderColor: "border-blue-200 dark:border-blue-800",
          textColor: "text-blue-800 dark:text-blue-200",
          title: t("review.underReview"),
          description: t("review.documentsBeingReviewed"),
        };
      case 2: // Approved
        return {
          statusCode: 2,
          icon: <CheckCircle className={`mx-2 h-5 w-5`} />,
          background: "bg-green-50 dark:bg-green-950",
          borderColor: "border-green-200 dark:border-green-800",
          textColor: "text-green-800 dark:text-green-200",
          title: t("approved.verified"),
          description: t("approved.accountVerified"),
        };
      case 3: // Refused
        return {
          statusCode: 4,
          icon: <XCircle className={`mx-2 h-5 w-5`} />,
          background: "bg-red-50 dark:bg-red-950",
          borderColor: "border-red-200 dark:border-red-800",
          textColor: "text-red-800 dark:text-red-200",
          title: t("refused.verificationFailed"),
          description: rejectionReason || t("refused.defaultMessage"),
        };
      default:
        return null;
    }
  };

  const statusConfig = getStatusConfig(profileStatus);

  if (!statusConfig) {
    return null;
  }

  return (
    <Card className={`${statusConfig.background} ${statusConfig.borderColor}`}>
      <CardHeader>
        <CardTitle className={`flex items-center ${statusConfig.textColor}`}>
          {statusConfig.icon}
          {statusConfig.title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className={`${statusConfig.textColor} opacity-90`}>
          {statusConfig.description}
        </div>
      </CardContent>
    </Card>
  );
}
